# Build stage
FROM node:20-alpine3.18 AS build

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++ 

# Copy package files and install production dependencies
COPY package.json yarn.lock ./
RUN yarn install --production

# Copy the rest of the app and build
COPY . .
RUN yarn build

# Production stage
FROM node:20-alpine3.18 AS production


WORKDIR /app

# Copy built app from build stage and set proper ownership
COPY --from=build --chown=node:node /app ./

ENV NODE_ENV=production
ENV PATH=/app/node_modules/.bin:$PATH

# Create cache directory and set proper permissions
RUN mkdir -p /home/<USER>/.cache && chown -R node:node /home/<USER>/.cache && chown -R node:node /app

# Switch to non-root user
USER node

EXPOSE 1337

CMD ["yarn", "start"]

# Generate a flow API request

Your task is making a request from the frontend to the backend.

Ask me for:

- The **name of the api** I want to create.
- The **endpoint backend**.
- The **sample backend response**.

Requirements:

- Add the request BE to the strapiClient in the file request.ts
- Add reducer in .slice file
- Add handle reducer in .saga file
- Add the selector if have a new state 
- Add the proxy API in the folder pages/api/...(This will integrate with StrapiClient)
- Add the dispatch event in the UI component where handle action

Remember: 
- Using typescript, 
- implement proxy API to integrate with StrapiClient
- implement fallback for the data

Examples:
- example of proxy API
```
// Next.js API route support: https://nextjs.org/docs/api-routes/introduction
import { TSocialSearchResults, AppError, ICategory, ITag } from "@/interfaces";
import { SampleData } from "@/utils/data";
import { StrapiClient } from "@/utils/request";
import type { NextApiRequest, NextApiResponse } from "next";
import { sendApiError } from "@/utils/api-error-handler";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ITag[] | AppError>
) {
  try {
    const response: any = await StrapiClient.getAffiliateTags();
    const tags: ITag[] = response.data;

    res.status(200).json(tags);
  } catch (error: any) {
    console.log(error);
    sendApiError(res, error, "Error fetching affiliate tags");
  }
}

```
- example .saga handler
```
import { call, put, takeEvery } from "redux-saga/effects";
import { actions } from "./category.slice";
import { ICategory } from "@/interfaces";

function* handleFetch(): Generator<any, void, any> {
  try {
    const response: any = yield call(fetch, "/api/categories");
    
    if (!response.ok) {
      const errorMsg = `Failed to fetch categories: ${response.status} ${response.statusText}`;
      console.error(errorMsg);
      yield put({ type: "FETCH_FAILED", error: errorMsg });
      return;
    }
    
    const { data } = yield response.json();
    yield put(actions.setCategories(data));
  } catch (error: any) {
    const errorMsg = error.message || "Failed to fetch categories";
    console.error(errorMsg);
    yield put({ type: "FETCH_FAILED", error });
  }
}

export default function* categorySaga() {
  yield takeEvery(actions.fetchAll.type, handleFetch);
  //   yield takeEvery(actions.create.type, handleCreate);
  // Add similar handlers for update/detail
}

```

example .slice file
```
import { ICategory } from "@/interfaces";
import { RootState } from "@/store";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { createSelector } from "reselect";

interface CategoryState {
  list: ICategory[];
  current?: ICategory;
}

const initialState: CategoryState = {
  list: [
    {
      id: "",
      documentId: "",
      name: "All Programs",
    },
  ],
  current: {
    id: "",
    documentId: "",
    name: "All Programs",
  },
};

const categorySlice = createSlice({
  name: "category",
  initialState,
  reducers: {
    // Sync actions
    setCategories: (state, action: PayloadAction<ICategory[]>) => {
      const newCategories = action.payload
        ? action.payload?.filter(
            (category) =>
              !state.list.some((existing) => existing.id === category.id)
          )
        : [];
      state.list = [...state.list, ...newCategories];
    },
    setCurrentCategory: (state, action: PayloadAction<ICategory>) => {
      state.current = action.payload;
    },

    // Trigger actions for Saga
    fetchAll: () => {},
  },
});

export const { actions, reducer } = categorySlice;
const selectCategoryState = (state: RootState) => state.category;

// category
export const selectCategories = createSelector(
  [selectCategoryState],
  (categoryState) => categoryState.list
);
export const selectFilterCategory = createSelector(
  [selectCategoryState],
  (categoryState) => categoryState.current
);

```

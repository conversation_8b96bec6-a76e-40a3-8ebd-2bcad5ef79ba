/**
 * Gets the redirect URL from the current URL or returns default path
 * @param defaultPath Default path to redirect to if no redirect parameter is found
 * @returns The URL to redirect to after authentication
 */
export const getRedirectUrlAfterAuth = (defaultPath: string = "/"): string => {
  // Only run in browser environment
  if (typeof window === "undefined") {
    return defaultPath;
  }
  
  try {
    // Get the current URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const redirectUrl = urlParams.get("redirect");
    
    // If there's a redirect parameter and it's a valid relative URL (not external)
    if (redirectUrl) {
      // Make sure it's a relative URL to prevent open redirect vulnerabilities
      // Check if it starts with / and doesn't contain :// (which would indicate protocol)
      if (redirectUrl.startsWith("/") && !redirectUrl.includes("://")) {
        return redirectUrl;
      }
    }
    
    // If no valid redirect parameter, return the default path
    return defaultPath;
  } catch (error) {
    console.error("Error parsing redirect URL:", error);
    return defaultPath;
  }
}

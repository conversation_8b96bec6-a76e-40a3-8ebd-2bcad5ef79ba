import React, { useState, useEffect, useRef } from "react";
import {
  Trash2,
  X,
  Loader2,
  Plus,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectReferrerLinks,
  selectReferrerLinksLoading,
  selectReferrerLinksError,
  selectReferrerLinksPagination,
} from "@/features/selectors";
import { referrerLinksActions } from "@/features/rootActions";
import { ReferrerLink } from "@/features/referrer-links/referrer-links.slice";
import TableLinks from "@/components/TableLinks";
import LinkModal, { LinkFormData } from "@/components/Modals/LinkModal";

// DeleteConfirmation component remains the same
const DeleteConfirmation: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  linkName: string;
  isLoading?: boolean;
}> = ({ isOpen, onClose, onConfirm, linkName, isLoading = false }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md overflow-hidden transform transition-all">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Delete Link
          </h3>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            disabled={isLoading}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Body */}
        <div className="p-6">
          <div className="flex items-center gap-4 mb-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-900/30">
            <div className="p-2 bg-red-100 dark:bg-red-900/30 rounded-full">
              <Trash2 className="w-5 h-5 text-red-600 dark:text-red-400" />
            </div>
            <p className="text-sm text-red-600 dark:text-red-400 font-medium">
              This action cannot be undone
            </p>
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Are you sure you want to delete{" "}
            <span className="font-semibold text-gray-900 dark:text-white">
              "{linkName}"
            </span>
            ?
          </p>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-gray-50 dark:bg-gray-750 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-650 border border-gray-300 dark:border-gray-600 transition-colors"
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center justify-center min-w-[80px] transition-colors shadow-sm"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Deleting...
              </>
            ) : (
              "Delete"
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

// New component to handle expandable URLs
const ExpandableUrl: React.FC<{ url: string }> = ({ url }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const urlRef = useRef<HTMLDivElement>(null);

  // Handle click for mobile devices
  const handleClick = () => {
    setIsExpanded(!isExpanded);
  };

  // Close tooltip when clicking outside
  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (
        urlRef.current &&
        !urlRef.current.contains(event.target as Node) &&
        isExpanded
      ) {
        setIsExpanded(false);
      }
    };

    document.addEventListener("mousedown", handleOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [isExpanded]);

  return (
    <div
      ref={urlRef}
      className="relative cursor-pointer"
      onClick={handleClick}
      onMouseEnter={() => setIsExpanded(true)}
      onMouseLeave={() => setIsExpanded(false)}
    >
      <span className="text-blue-600 hover:text-blue-800 dark:text-blue-400 truncate max-w-[180px] inline-block">
        {url}
      </span>

      {isExpanded && (
        <div className="absolute z-10 left-0 -bottom-1 transform translate-y-full bg-white dark:bg-gray-800 shadow-lg rounded-md py-2 px-3 text-sm max-w-xs break-all border border-gray-200 dark:border-gray-700">
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400"
            onClick={(e) => e.stopPropagation()}
          >
            {url}
          </a>
        </div>
      )}
    </div>
  );
};

// Add this interface for pagination
interface Pagination {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

// Pagination Controls Component
const PaginationControls: React.FC<{
  currentPage: number;
  pageCount: number;
  onPageChange: (page: number) => void;
  isLoading: boolean;
}> = ({ currentPage, pageCount, onPageChange, isLoading }) => {
  // Calculate the range of page numbers to display
  const getPageNumbers = () => {
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(pageCount, startPage + maxVisiblePages - 1);

    // Adjust start page if end page is at max
    if (endPage === pageCount) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    return Array.from(
      { length: endPage - startPage + 1 },
      (_, i) => startPage + i
    );
  };

  if (pageCount <= 1) return null;

  return (
    <div className="flex items-center justify-center space-x-2">
      {/* First Page */}
      <button
        onClick={() => onPageChange(1)}
        disabled={currentPage === 1 || isLoading}
        className={`p-2 rounded ${
          currentPage === 1
            ? "text-gray-400 cursor-not-allowed"
            : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
        }`}
        aria-label="First page"
      >
        <ChevronsLeft className="w-4 h-4" />
      </button>

      {/* Previous Page */}
      <button
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1 || isLoading}
        className={`p-2 rounded ${
          currentPage === 1
            ? "text-gray-400 cursor-not-allowed"
            : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
        }`}
        aria-label="Previous page"
      >
        <ChevronLeft className="w-4 h-4" />
      </button>

      {/* Page Numbers */}
      {getPageNumbers().map((pageNum) => (
        <button
          key={pageNum}
          onClick={() => onPageChange(pageNum)}
          disabled={isLoading}
          className={`w-9 h-9 rounded flex items-center justify-center ${
            currentPage === pageNum
              ? "bg-blue-600 text-white"
              : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
          }`}
        >
          {pageNum}
        </button>
      ))}

      {/* Next Page */}
      <button
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === pageCount || isLoading}
        className={`p-2 rounded ${
          currentPage === pageCount
            ? "text-gray-400 cursor-not-allowed"
            : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
        }`}
        aria-label="Next page"
      >
        <ChevronRight className="w-4 h-4" />
      </button>

      {/* Last Page */}
      <button
        onClick={() => onPageChange(pageCount)}
        disabled={currentPage === pageCount || isLoading}
        className={`p-2 rounded ${
          currentPage === pageCount
            ? "text-gray-400 cursor-not-allowed"
            : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
        }`}
        aria-label="Last page"
      >
        <ChevronsRight className="w-4 h-4" />
      </button>
    </div>
  );
};

const LinksOverview: React.FC = () => {
  const dispatch = useDispatch();
  const links = useSelector(selectReferrerLinks);
  const isLoading = useSelector(selectReferrerLinksLoading);
  const error = useSelector(selectReferrerLinksError);
  const pagination = useSelector(
    selectReferrerLinksPagination
  ) as Pagination | null;

  const [currentPage, setCurrentPage] = useState(1);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentLink, setCurrentLink] = useState<ReferrerLink | null>(null); // Use ReferrerLink instead of LinkData
  const [copiedId, setCopiedId] = useState<string | null>(null);

  // Track previous loading state to detect when operations complete
  const [prevLoadingState, setPrevLoadingState] = useState(false);

  // Fetch links on component mount
  useEffect(() => {
    dispatch(
      referrerLinksActions.fetchLinks({ page: currentPage, pageSize: 25 })
    );
  }, [dispatch, currentPage]);

  // Close modals when loading changes from true to false (operation completed)
  useEffect(() => {
    // If loading was true and now it's false, and there's no error, close modals
    if (prevLoadingState && !isLoading && !error) {
      if (isCreateModalOpen || isEditModalOpen) {
        setIsCreateModalOpen(false);
        setIsEditModalOpen(false);
      }
    }
    // Update previous loading state
    setPrevLoadingState(isLoading);
  }, [isLoading, error, isCreateModalOpen, isEditModalOpen, prevLoadingState]);

  // Replace handleShowMoreClick with handlePageChange
  const handlePageChange = (pageNum: number) => {
    setCurrentPage(pageNum);
    // Scroll to top of table when changing pages
    document
      .querySelector(".overflow-x-auto")
      ?.scrollIntoView({ behavior: "smooth" });
  };

  const copyToClipboard = (text: string, id: string) => {
    navigator.clipboard.writeText(text);
    setCopiedId(id);

    // Reset copied status after 2 seconds
    setTimeout(() => {
      setCopiedId(null);
    }, 2000);
  };

  const handleClearError = () => {
    dispatch(referrerLinksActions.clearError());
  };

  const handleCreateLink = (data: LinkFormData) => {
    dispatch(referrerLinksActions.createLink(data));
    // Don't close modal immediately - let the saga handle success/error
  };

  const handleEditLink = (data: LinkFormData) => {
    if (!currentLink || !currentLink.documentId) return; // Add null check for documentId

    dispatch(
      referrerLinksActions.updateLink({
        id: currentLink.id,
        documentId: currentLink.documentId,
        name: data.name,
        url: data.url,
        shortLink: data.shortLink,
      })
    );
    // Don't close modal immediately - let the saga handle success/error
  };

  const handleDeleteLink = () => {
    if (!currentLink || !currentLink.documentId) return; // Add null check for documentId

    dispatch(
      referrerLinksActions.deleteLink({
        id: currentLink.id,
        documentId: currentLink.documentId,
      })
    );
    setIsDeleteModalOpen(false);
  };

  const openEditModal = (link: ReferrerLink) => {
    // Update parameter type
    setCurrentLink(link);
    setIsEditModalOpen(true);
  };

  const openDeleteModal = (link: ReferrerLink) => {
    // Update parameter type
    setCurrentLink(link);
    setIsDeleteModalOpen(true);
  };

  // Helper function to parse URL into path and viaValue
  const parseUrl = (url: string): { path: string; viaValue: string } => {
    try {
      const urlObj = new URL(url);
      // Extract path without leading slash
      const path = urlObj.pathname.replace(/^\//, "");
      // Extract via parameter
      const viaValue = urlObj.searchParams.get("via") || "";

      return { path, viaValue };
    } catch (error) {
      // If URL parsing fails, return default values
      console.error("Error parsing URL:", error);
      return { path: "", viaValue: "" };
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700">
      <div className="flex justify-between items-center p-6">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white">
          Your Links
        </h2>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-sm flex items-center transition-colors"
          disabled={isLoading}
        >
          <Plus className="w-4 h-4 mr-1.5" />
          Create Link
        </button>
      </div>

      {/* Remove the separate loading state and let TableLinks handle it */}
      <TableLinks
        links={links}
        isLoading={isLoading}
        onEdit={openEditModal}
        onDelete={openDeleteModal}
        onCopy={copyToClipboard}
        copiedId={copiedId}
        enablePagination={false}
        pagination={pagination}
        currentPage={currentPage}
        onPageChange={handlePageChange}
        isPaginationLoading={isLoading && currentPage !== 1}
      />

      {/* Create Link Modal - Now using the shared component */}
      <LinkModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateLink}
        title="Create New Link"
        isLoading={isLoading}
        apiError={error}
        onClearError={handleClearError}
      />

      {/* Edit Link Modal - Now using the shared component */}
      <LinkModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        onSubmit={handleEditLink}
        initialData={
          currentLink
            ? (() => {
                const { path, viaValue } = parseUrl(currentLink.url);
                return {
                  name: currentLink.name,
                  path: path,
                  viaValue: viaValue,
                  shortLink: currentLink.short_link || "",
                  url: currentLink.url,
                };
              })()
            : undefined
        }
        title="Edit Link"
        isLoading={isLoading}
        apiError={error}
        onClearError={handleClearError}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmation
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteLink}
        linkName={currentLink?.name || ""}
        isLoading={isLoading}
      />
    </div>
  );
};

export default LinksOverview;

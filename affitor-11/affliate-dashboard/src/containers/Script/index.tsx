import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  useTheme,
  useMediaQuery,
  Switch,
  FormControlLabel
} from "@mui/material";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import ClearIcon from "@mui/icons-material/Clear";
import DownloadIcon from "@mui/icons-material/Download";

// SVG Icon Components for the new design
const PlayIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <polygon points="5,3 19,12 5,21"></polygon>
  </svg>
);

const BookmarkIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
  </svg>
);

const XIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path d="M18 6L6 18"></path>
    <path d="M6 6L18 18"></path>
  </svg>
);

const TargetIcon = () => (
  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="10"></circle>
    <circle cx="12" cy="12" r="6"></circle>
    <circle cx="12" cy="12" r="2"></circle>
  </svg>
);

const LightbulbIcon = () => (
  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path d="M9 21h6"></path>
    <path d="M12 3C8.5 3 5.5 6 5.5 9.5c0 2.5 1.5 4.5 3.5 6v1a2 2 0 0 0 2 2h2a2 2 0 0 0 2-2v-1c2-1.5 3.5-3.5 3.5-6C18.5 6 15.5 3 12 3z"></path>
  </svg>
);

const SettingsIcon = () => (
  <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
    <circle cx="12" cy="12" r="3"></circle>
  </svg>
);

const ArrowRightIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path d="M5 12h14"></path>
    <path d="m12 5 7 7-7 7"></path>
  </svg>
);

const ShieldIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
  </svg>
);

const ClockIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="10"></circle>
    <polyline points="12,6 12,12 16,14"></polyline>
  </svg>
);

const GlobeIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="2" x2="22" y1="12" y2="12"></line>
    <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
  </svg>
);

const FileTextIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
    <polyline points="14,2 14,8 20,8"></polyline>
    <line x1="16" x2="8" y1="13" y2="13"></line>
    <line x1="16" x2="8" y1="17" y2="17"></line>
    <polyline points="10,9 9,9 8,9"></polyline>
  </svg>
);

const Edit3Icon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path d="M12 20h9"></path>
    <path d="M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
  </svg>
);

const ChevronDownIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path d="M6 9l6 6 6-6"></path>
  </svg>
);

// Simple Accordion Components
interface AccordionItemProps {
  children: React.ReactNode;
  className?: string;
}

interface AccordionChildProps {
  isOpen?: boolean;
  setIsOpen?: (open: boolean) => void;
}

const AccordionItem = ({ children, className = "" }: AccordionItemProps) => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div className={className}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child as React.ReactElement<AccordionChildProps>, {
            isOpen,
            setIsOpen
          });
        }
        return child;
      })}
    </div>
  );
};

const AccordionTrigger = ({ 
  children, 
  className = "", 
  isOpen, 
  setIsOpen 
}: { 
  children: React.ReactNode; 
  className?: string; 
  isOpen?: boolean; 
  setIsOpen?: (open: boolean) => void; 
}) => {
  return (
    <button
      className={`${className} flex justify-between items-center w-full text-left`}
      onClick={() => setIsOpen && setIsOpen(!isOpen)}
    >
      {children}
      <ChevronDownIcon />
    </button>
  );
};

const AccordionContent = ({ 
  children, 
  className = "", 
  isOpen 
}: { 
  children: React.ReactNode; 
  className?: string; 
  isOpen?: boolean; 
}) => {
  return (
    <div className={`${isOpen ? 'block' : 'hidden'} ${className}`}>
      {children}
    </div>
  );
};

const UploadIcon = () => (
  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
    <polyline points="17,8 12,3 7,8"></polyline>
    <line x1="12" x2="12" y1="3" y2="15"></line>
  </svg>
);

const ZapIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <polygon points="13,2 3,14 12,14 11,22 21,10 12,10 13,2"></polygon>
  </svg>
);

const DownloadIconSVG = () => (
  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
    <polyline points="7,10 12,15 17,10"></polyline>
    <line x1="12" x2="12" y1="15" y2="3"></line>
  </svg>
);

// Function to extract video ID from YouTube URL
const extractYouTubeVideoId = (url: string): string | null => {
  const regExp =
    /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=|shorts\/)([^#&?]*).*/;
  const match = url.match(regExp);

  return match && match[2].length === 11 ? match[2] : null;
};

const ScriptContainer: React.FC = () => {
  const [youtubeUrl, setYoutubeUrl] = useState("");
  const [transcript, setTranscript] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copySuccess, setCopySuccess] = useState(false);
  const [showBookmarkBanner, setShowBookmarkBanner] = useState(true);
  const [hasTranscript, setHasTranscript] = useState(false);

  // Only keep the timestamps toggle, remove dark mode
  const [showTimestamps, setShowTimestamps] = useState(true);
  // Remove dark transcript state

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  // FAQ data
  const faqData = [
    {
      question: "How do I get a Free YouTube Transcript from any video?",
      answer: "Simply paste the video link into Affitor's YouTube transcript generator. You'll get a free YouTube transcript instantly — no login or extensions required."
    },
    {
      question: "What should I do if the transcript is not showing up for a YouTube video?",
      answer: "If the transcript is missing or disabled, Affitor can still generate a free YouTube transcript using AI — even for long videos or videos without captions."
    },
    {
      question: "Can Affitor generate Free YouTube Transcripts in other languages?",
      answer: "Yes, Affitor supports multiple languages. You can get a free YouTube transcript in English, Spanish, Vietnamese, and more — just paste the link."
    },
    {
      question: "How accurate is the Free YouTube Transcript generated by Affitor?",
      answer: "Affitor delivers high-accuracy transcripts powered by AI. For most clear-audio videos, the free YouTube transcript is 90–95% accurate."
    },
    {
      question: "Does the Free YouTube Transcript tool work with long YouTube videos?",
      answer: "Yes. Affitor can handle long YouTube videos and generate complete transcripts — even when the original transcript is not showing up."
    },
    {
      question: "Can I edit or customize the Free YouTube Transcript after it's generated?",
      answer: "Absolutely. Once generated, you can edit your free YouTube transcript directly in the dashboard, remove timestamps, or download as clean text."
    }
  ];

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setYoutubeUrl(e.target.value);
  };

  const handleGetTranscript = async () => {
    if (!youtubeUrl.trim()) {
      setError("Please enter a YouTube URL");
      return;
    }

    setLoading(true);
    setError(null);
    setTranscript("");

    try {
      // Extract video ID from the URL
      const videoId = extractYouTubeVideoId(youtubeUrl);

      if (!videoId) {
        setError("Invalid YouTube URL. Could not extract video ID.");
        setLoading(false);
        return;
      }

      // Call our API endpoint directly
      const response = await fetch(
        `/api/social-listenings/custom-transcript/${videoId}`
      );
      const data = await response.json();

      if (!data.success || data.error) {
        setError(data.error || "No transcript found for this video.");
      } else if (!data.transcript) {
        setError("No transcript found for this video.");
      } else {
        setTranscript(data.transcript);
        setHasTranscript(true);
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyToClipboard = () => {
    if (transcript) {
      navigator.clipboard
        .writeText(transcript)
        .then(() => {
          setCopySuccess(true);
        })
        .catch(() => {
          setError("Failed to copy to clipboard");
        });
    }
  };

  const handleDownloadTranscript = () => {
    if (transcript) {
      const blob = new Blob([transcript], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'youtube-transcript.txt';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const handleCloseSnackbar = () => {
    setCopySuccess(false);
  };

  const handleClearInput = () => {
    setYoutubeUrl("");
  };

  const handleClearTranscript = () => {
    setTranscript("");
  };

  // Updated function to parse the new transcript format with single timestamps
  const formatTranscriptWithTimestamps = (text: string) => {
    // Pattern to match "00:00 : text" format
    const timestampRegex =
      /(\d{1,2}:\d{2})\s*:\s*([\s\S]*?)(?=\n\d{1,2}:\d{2}\s*:|\n$|$)/g;
    const matches = Array.from(text.matchAll(timestampRegex));

    if (matches && matches.length > 0) {
      return matches.map((match, index) => {
        const timestamp = match[1];
        const content = match[2].trim();
        const isMusic =
          content.includes("[âm nhạc]") || content.includes("[music]");

        return {
          timestamp,
          content,
          id: index,
          isMusic,
        };
      });
    } else {
      // Fallback if the regex doesn't match
      return text
        .split("\n")
        .filter((line) => line.trim())
        .map((line, index) => ({
          timestamp: "",
          content: line,
          id: index,
          isMusic: false,
        }));
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-white dark:from-gray-900 dark:to-gray-800">
      {/* Bookmark Banner */}
      {showBookmarkBanner && (
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 dark:from-blue-700 dark:to-blue-800 text-white py-2 px-4 relative">
          <div className="max-w-7xl mx-auto flex items-center justify-center gap-3">
            <BookmarkIcon />
            <span className="text-sm font-medium">
              Quick access! Press{' '}
              <kbd className="px-2 py-0.5 bg-white/20 dark:bg-white/30 rounded text-xs mx-1">
                Ctrl + D
              </kbd>
              to bookmark us!
            </span>
            <button
              onClick={() => setShowBookmarkBanner(false)}
              className="absolute right-4 p-1 hover:bg-white/10 dark:hover:bg-white/20 rounded transition-colors"
            >
              <XIcon />
            </button>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="relative">
        {/* Hero Section */}
        <section className="relative py-12 lg:py-16">
          <div className="max-w-4xl mx-auto px-6 lg:px-8 text-center">
            {/* Bookmark CTA */}
            <div className="flex items-center justify-center gap-3 mb-8">
              <div className="flex items-center gap-2 px-4 py-2 bg-blue-50 dark:bg-blue-900/30 rounded-full border border-blue-100 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors cursor-pointer">
                <BookmarkIcon />
                <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                  <kbd className="px-1.5 py-0.5 bg-blue-200/50 dark:bg-blue-800/50 rounded text-xs mr-1">
                    Ctrl + D
                  </kbd>
                  Bookmark us!
                </span>
              </div>
            </div>

            {/* Main Heading */}
            <div className="space-y-6 mb-12">
              <h1 className="text-5xl lg:text-6xl text-primary-foreground tracking-tight font-medium">
                Free YouTube Transcript
              </h1>
              <p className="text-xl text-primary-foreground/90 leading-relaxed max-w-3xl mx-auto">
                Easily get a text of YouTube video transcript. Paste the YouTube video URL.
                Download or copy the transcript with timestamps — for free YouTube videos.
              </p>
            </div>

            {/* URL Input Section */}
            <div className="max-w-2xl mx-auto space-y-6 mb-16">
              <div className="flex items-center justify-center gap-2 text-primary-foreground/90">
                <PlayIcon />
                <span className="text-sm font-medium">Paste YouTube Video URL</span>
              </div>
              
              <div className="relative">
                <div className="flex gap-3">
                  <div className="flex-1 relative">
                    <input
                      type="text"
                      placeholder="https://www.youtube.com/watch?v=..."
                      value={youtubeUrl}
                      onChange={(e) => setYoutubeUrl(e.target.value)}
                      className="w-full h-14 px-6 text-base bg-white dark:bg-gray-800 border border-slate-200 dark:border-gray-600 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 outline-none text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
                      disabled={loading}
                    />
                  </div>
                  <button 
                    onClick={handleGetTranscript}
                    disabled={!youtubeUrl.trim() || loading}
                    className="h-14 px-8 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 disabled:bg-slate-300 dark:disabled:bg-gray-600 disabled:text-slate-500 dark:disabled:text-gray-400 disabled:cursor-not-allowed rounded-xl shadow-sm hover:shadow-md transition-all duration-200 text-base font-medium text-white"
                  >
                    {loading ? "..." : "Generate"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Loading indicator */}
        {loading && (
          <div className="max-w-4xl mx-auto mb-20">
            <div className="p-8 shadow-lg border-0 bg-white dark:bg-gray-800 rounded-2xl">
              <div className="flex items-center justify-center gap-3">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: "0ms" }}></div>
                  <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: "200ms" }}></div>
                  <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: "400ms" }}></div>
                </div>
                <span className="text-sm text-primary-foreground/90 dark:text-gray-300">
                  Generating transcript...
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="max-w-4xl mx-auto mb-8">
            <div className="p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl">
              <div className="flex items-center gap-3">
                <div className="w-5 h-5 text-red-500 dark:text-red-400">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <span className="text-red-700 dark:text-red-300 font-medium">{error}</span>
              </div>
            </div>
          </div>
        )}

        {/* Transcript Result */}
        {transcript && (
          <div className="max-w-4xl mx-auto mb-20">
            <div className="p-8 shadow-lg border-0 bg-white dark:bg-gray-800 rounded-2xl">
              <div className="flex flex-col gap-8">
                <div className="flex items-center justify-between pb-4 border-b border-slate-100 dark:border-gray-700 flex-wrap gap-4">
                  <span className="text-slate-700 dark:text-gray-200 font-semibold text-lg">Transcript Results</span>
                  <div className="flex items-center gap-3 flex-wrap">
                    <FormControlLabel
                      control={
                        <Switch
                          checked={showTimestamps}
                          onChange={(e) => setShowTimestamps(e.target.checked)}
                          color="primary"
                          size="small"
                        />
                      }
                      label={
                        <span className="text-sm text-slate-700 dark:text-gray-200">
                          Show Timestamps
                        </span>
                      }
                      className="m-0"
                    />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-3 flex-wrap">
                  <button
                    onClick={handleCopyToClipboard}
                    className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 text-white rounded-lg transition-colors duration-200"
                  >
                    <ContentCopyIcon className="w-4 h-4" />
                    Copy Transcript
                  </button>
                  <button
                    onClick={handleDownloadTranscript}
                    className="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-600 text-white rounded-lg transition-colors duration-200"
                  >
                    <DownloadIcon className="w-4 h-4" />
                    Download
                  </button>
                  <button
                    onClick={handleClearTranscript}
                    className="flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 text-white rounded-lg transition-colors duration-200"
                  >
                    <ClearIcon className="w-4 h-4" />
                    Clear
                  </button>
                </div>

                {/* Transcript Content */}
                <div className="bg-gray-50 dark:bg-gray-900 rounded-xl p-6 max-h-96 overflow-y-auto">
                  {showTimestamps ? (
                    <div className="space-y-3">
                      {formatTranscriptWithTimestamps(transcript).map((item) => (
                        <div key={item.id} className={`flex gap-4 ${item.isMusic ? 'opacity-60' : ''}`}>
                          <span className="text-blue-600 dark:text-blue-400 font-mono text-sm min-w-[60px] flex-shrink-0">
                            {item.timestamp}
                          </span>
                          <span className="text-gray-800 dark:text-gray-200 leading-relaxed">
                            {item.content}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-gray-800 dark:text-gray-200 leading-relaxed whitespace-pre-wrap">
                      {transcript.replace(/\d{1,2}:\d{2}\s*:\s*/g, '')}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Features Section */}
        <section className="py-16 bg-white dark:bg-gray-900">
          <div className="max-w-6xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl text-slate-900 dark:text-gray-100 mb-6 font-medium">
                From Transcript to Action
              </h2>
              <p className="text-xl text-primary-foreground/90 dark:text-gray-300 leading-relaxed max-w-3xl mx-auto">
                Transform your YouTube transcripts into powerful tools for learning, 
                content creation, and optimization with our advanced features.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="group p-8 border border-slate-100 dark:border-gray-700 shadow-sm hover:shadow-lg transition-all duration-300 rounded-2xl bg-gradient-to-br from-orange-50 to-white dark:from-orange-900/20 dark:to-gray-800">
                <div className="space-y-6">
                  <div className="w-48 h-48 bg-white dark:bg-gray-700 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300 overflow-hidden">
                    <img 
                      src="/script/7.png" 
                      alt="Learn Faster" 
                      className="w-full h-full object-contain p-4"
                    />
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-xl text-slate-900 dark:text-gray-100 font-medium">Learn Faster, Smarter</h3>
                    <p className="text-primary-foreground/90 dark:text-gray-300 leading-relaxed">
                      Transform video content into searchable text. Quickly find important information 
                    </p>
                  </div>
                </div>
              </div>

              <div className="group p-8 border border-slate-100 dark:border-gray-700 shadow-sm hover:shadow-lg transition-all duration-300 rounded-2xl bg-gradient-to-br from-pink-50 to-white dark:from-pink-900/20 dark:to-gray-800">
                <div className="space-y-6">
                  <div className="w-48 h-48 bg-white dark:bg-gray-700 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300 overflow-hidden">
                    <img 
                      src="/script/8.png" 
                      alt="Create Content" 
                      className="w-full h-full object-contain p-4"
                    />
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-xl text-slate-900 dark:text-gray-100 font-medium">Create Content That Stands Out</h3>
                    <p className="text-primary-foreground/90 dark:text-gray-300 leading-relaxed">
                      Use AI insights to transform YouTube transcripts into content summaries, 
                      blog posts, and relevant content for your audience.
                    </p>
                  </div>
                </div>
              </div>

              <div className="group p-8 border border-slate-100 dark:border-gray-700 shadow-sm hover:shadow-lg transition-all duration-300 rounded-2xl bg-gradient-to-br from-blue-50 to-white dark:from-blue-900/20 dark:to-gray-800">
                <div className="space-y-6">
                  <div className="w-48 h-48 bg-white dark:bg-gray-700 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300 overflow-hidden">
                    <img 
                      src="/script/9.png" 
                      alt="Maximize Learning" 
                      className="w-full h-full object-contain p-4"
                    />
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-xl text-slate-900 dark:text-gray-100 font-medium">Maximize Your Learning</h3>
                    <p className="text-primary-foreground/90 dark:text-gray-300 leading-relaxed">
                      Perfect for content creators and learners. Optimize your content strategy 
                      using transcript analysis and insights.
                    </p>
                    <a href="/pricing" className="group/btn mt-4 hover:bg-blue-50 dark:hover:bg-blue-900/30 border-blue-200 dark:border-blue-700 border px-4 py-2 rounded-lg flex items-center gap-2 transition-colors text-blue-600 dark:text-blue-400">
                      Explore Pricing
                      <ArrowRightIcon />
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section className="py-20 bg-slate-50 dark:bg-gray-800">
          <div className="max-w-5xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl text-slate-900 dark:text-gray-100 mb-6 font-medium">
                How to Get YouTube Transcript
              </h2>
              <p className="text-xl text-primary-foreground/90 dark:text-gray-300 leading-relaxed">
                Get transcripts of YouTube videos in three simple steps using Afflitor.
              </p>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        {hasTranscript && (
          <section className="py-16 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30">
            <div className="max-w-4xl mx-auto px-6 lg:px-8">
              <div className="p-8 border-0 shadow-lg rounded-2xl bg-white dark:bg-gray-800">
                <div className="flex flex-col lg:flex-row items-center gap-6">
                  <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 rounded-2xl overflow-hidden flex-shrink-0 shadow-lg">
                    <img 
                      src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop" 
                      alt="AI illustration"
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1 text-center lg:text-left">
                    <h3 className="text-2xl text-slate-900 dark:text-gray-100 mb-2 font-medium">
                      The all-in-one AI tool to boost your Affiliate Marketing success
                    </h3>
                    <p className="text-primary-foreground/90 dark:text-gray-300 mb-6 text-lg">
                      Automate your digital advertising. Amplify better performance.
                    </p>
                    <a href="/pricing" className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 h-12 px-8 text-base shadow-lg hover:shadow-xl transition-all duration-200 text-white rounded-lg inline-flex items-center gap-2">
                      Start for Free
                      <ArrowRightIcon />
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </section>
        )}

        {/* FAQ Section */}
        <section className="py-16 bg-slate-50 dark:bg-gray-800">
          <div className="max-w-4xl mx-auto px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl text-slate-900 dark:text-gray-100 mb-6 font-medium">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-primary-foreground/90 dark:text-gray-300">
                Everything you need to know about our YouTube transcript generator.
              </p>
            </div>
          </div>
        </section>

        <div className="py-8 text-center bg-slate-50 dark:bg-gray-800">
          <p className="text-sm text-slate-500 dark:text-gray-400">
            Note: This tool can only extract transcripts from videos that have captions enabled.
          </p>
        </div>
      </main>

      {/* Success Snackbar */}
      <Snackbar
        open={copySuccess}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
          Transcript copied to clipboard!
        </Alert>
      </Snackbar>
    </div>
  );
};

export default ScriptContainer;

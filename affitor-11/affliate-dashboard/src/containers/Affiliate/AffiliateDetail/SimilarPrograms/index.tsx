import { useEffect } from "react";
import { TableAffiliate } from "@/components/TableAffiliate";
import { useDispatch, useSelector } from "react-redux";
import { affiliateActions } from "@/features/rootActions";
import {
  selectAffiliatePrograms,
  selectCurrentAffiliate,
} from "@/features/selectors";

export default function SimilarPrograms() {
  const dispatch = useDispatch();
  const similarAffiliates = useSelector(selectAffiliatePrograms);
  const currentAffiliate = useSelector(selectCurrentAffiliate);
  const { fetch: fetchAffiliate } = affiliateActions;

  useEffect(() => {
    if (
      currentAffiliate &&
      currentAffiliate.categories &&
      currentAffiliate.categories.length > 0
    ) {
      dispatch(
        fetchAffiliate({
          pagination: {
            page: 1,
            pageSize: 5,
          },
          sort: [
            {
              field: "monthly_traffic",
              order: "desc",
            },
          ],
          filters: {
            documentId: {
              $ne: currentAffiliate.documentId,
            },
            categories: currentAffiliate.categories[0].id,
          },
        })
      );
    }
  }, [currentAffiliate]);
  return (
    <div className="p-4 bg-primary shadow rounded-lg text-[12px] md:text-[13px] h-full space-y-3">
      <div className="flex flex-col sm:flex-row sm:justify-between md:items-center relative">
        <h2 className="text-lg md:text-xl font-bold mb-4 text-primary-foreground sm:mb-0">
          Similar Programs
        </h2>
        {similarAffiliates && similarAffiliates.length > 0 && (
          <div className="flex justify-end">
            <button
              className="flex items-center gap-2 px-3 py-1.5 md:px-4 md:py-2 rounded-lg bg-secondary 
                      hover:bg-blue-500 hover:text-white transition-color shadow-sm text-[12px]
                       md:text-[14px] whitespace-nowrap text-primary-foreground dark:hover:bg-blue-500"
              onClick={() =>
                window.dispatchEvent(new CustomEvent("toggleColumnsModal"))
              }
            >
              <i className="fas fa-sliders-h"></i>
              Customize Columns
            </button>
          </div>
        )}
      </div>
      <div className="h-fit flex">
        {similarAffiliates && similarAffiliates.length > 0 ? (
          <TableAffiliate
            data={similarAffiliates}
            isPagination={false}
            pageSize={5}
            tableId="similar-programs"
          />
        ) : (
          <div className="text-center w-full py-4 text-gray-500 dark:text-gray-400">
            No similar programs found.
          </div>
        )}
      </div>
    </div>
  );
}

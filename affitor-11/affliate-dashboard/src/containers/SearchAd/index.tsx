import React, { useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Search,
  Filter,
  Loader2,
  AlertCircle,
  Eye,
  BarChart3,
  Target,
  Sparkles,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import FacebookCard from "./FacebookCard";
import TiktokCard from "./TiktokCard";
import YoutubeCard from "./YoutubeCard";
import { spyheroActions } from "@/features/rootActions";
import {
  selectSpyHeroAds,
  selectSpyHeroLoading,
  selectSpyHeroError,
  selectSpyHeroSearchKeyword,
  selectSpyHeroSearchPlatform,
  selectSpyHeroTotalCount,
  selectSpyHeroLastCrawled,
} from "@/features/selectors";
import { SpyHeroAd } from "@/features/spyhero/spyhero.slice";
import { AppDispatch } from "@/store";

const SearchAd: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();

  // Redux state
  const ads = useSelector(selectSpyHeroAds);
  const loading = useSelector(selectSpyHeroLoading);
  const error = useSelector(selectSpyHeroError);
  const searchKeyword = useSelector(selectSpyHeroSearchKeyword);
  const searchPlatform = useSelector(selectSpyHeroSearchPlatform);
  const totalCount = useSelector(selectSpyHeroTotalCount);
  const lastCrawled = useSelector(selectSpyHeroLastCrawled);

  // Local state
  const [keyword, setKeyword] = useState(searchKeyword || "");
  const [platform, setPlatform] = useState(searchPlatform || "youtube");

  // Handle search
  const handleSearch = useCallback(() => {
    if (!keyword.trim()) return;

    dispatch(
      spyheroActions.searchAdsRequest({
        keyword: keyword.trim(),
        platform,
      })
    );
  }, [dispatch, keyword, platform]);

  // Handle platform change
  const handlePlatformChange = useCallback(
    (value: string) => {
      setPlatform(value);
      dispatch(spyheroActions.setSearchPlatform(value));
    },
    [dispatch]
  );

  // Handle clear results
  const handleClearResults = useCallback(() => {
    dispatch(spyheroActions.clearSearchResults());
    setKeyword("");
  }, [dispatch]);

  // Handle Enter key press
  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter") {
        handleSearch();
      }
    },
    [handleSearch]
  );

  // Format number with K/M suffix
  const formatNumber = (num: string | number | undefined) => {
    if (!num) return "0";

    // Convert to string first if it's a number
    const numStr = typeof num === "number" ? num.toString() : num;

    // Extract numeric value from string (handles cases like "$1,234" or "1.5M")
    const cleanedStr = numStr.replace(/[^0-9.]/g, "");
    const n = parseFloat(cleanedStr);

    // Return original if parsing failed
    if (isNaN(n)) return numStr;

    if (n >= 1000000) return `${(n / 1000000).toFixed(1)}M`;
    if (n >= 1000) return `${(n / 1000).toFixed(1)}K`;
    return n.toString();
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch {
      return dateString;
    }
  };

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // Render ad card based on platform
  const renderAdCard = (ad: SpyHeroAd) => {
    const isTikTok = ad.platform === "tiktok";
    const isYouTube = ad.platform === "youtube";
    const isFacebook = ad.platform === "facebook";

    // Use dedicated card components based on platform
    if (isFacebook) {
      return (
        <FacebookCard
          key={ad.id}
          ad={ad}
          formatNumber={formatNumber}
          formatDate={formatDate}
          copyToClipboard={copyToClipboard}
        />
      );
    }

    if (isTikTok) {
      return (
        <TiktokCard
          key={ad.id}
          ad={ad}
          formatNumber={formatNumber}
          formatDate={formatDate}
          copyToClipboard={copyToClipboard}
        />
      );
    }

    if (isYouTube) {
      return (
        <YoutubeCard
          key={ad.id}
          ad={ad}
          formatNumber={formatNumber}
          formatDate={formatDate}
          copyToClipboard={copyToClipboard}
        />
      );
    }

    // Fallback for unknown platforms (should not happen)
    return null;
  };

  return (
    <div className="min-h-screen bg-background transition-colors duration-300">
      <div className="container mx-auto px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8 space-y-4 sm:space-y-6 lg:space-y-8">
        {/* Enhanced Header */}
        <div className="text-center space-y-4 sm:space-y-6 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 rounded-3xl blur-3xl -z-10 animate-pulse" />
          <div className="relative">
            <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-3 mb-3 sm:mb-4">
              <div className="p-2 sm:p-3 bg-primary/10 rounded-xl sm:rounded-2xl">
                <Sparkles className="w-6 h-6 sm:w-8 sm:h-8 text-primary animate-pulse" />
              </div>
              <h1 className="text-2xl sm:text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-primary via-primary/80 to-primary bg-clip-text text-center">
                Search Ads
              </h1>
            </div>
          </div>
        </div>

        {/* Modern Search Controls */}
        <div className="relative">
          {/* Enhanced background blur effect with proper colors */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/5 to-blue-500/10 rounded-3xl blur-xl -z-10" />

          <Card className="border-0 shadow-2xl bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl">
            <CardContent className="p-4 sm:p-6 lg:p-8">
              {/* Main Search Bar */}
              <div className="max-w-4xl mx-auto space-y-4 sm:space-y-6">
                <div className="relative group">
                  {/* Enhanced hover glow effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/30 via-purple-500/20 to-blue-500/30 rounded-xl sm:rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  {/* Mobile-first responsive layout */}
                  <div className="relative flex flex-col sm:flex-row items-stretch sm:items-center bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700 rounded-xl sm:rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 group-hover:border-blue-400 dark:group-hover:border-blue-500">
                    {/* Search Input Section */}
                    <div className="flex-1 flex items-center order-1">
                      <Search className="w-5 h-5 sm:w-6 sm:h-6 text-gray-400 dark:text-gray-500 ml-4 sm:ml-6 group-hover:text-blue-500 transition-colors duration-300" />
                      <Input
                        placeholder="Search keywords, brands..."
                        value={keyword}
                        onChange={(e) => setKeyword(e.target.value)}
                        onKeyPress={handleKeyPress}
                        className="border-0 bg-transparent text-base sm:text-lg px-3 sm:px-4 py-4 sm:py-6 focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-gray-400 dark:placeholder:text-gray-500 text-gray-900 dark:text-white"
                      />
                    </div>

                    {/* Platform Selector - Mobile: full width, Desktop: inline */}
                    <div className="flex items-center border-t sm:border-t-0 sm:border-l border-gray-200 dark:border-gray-700 px-4 py-2 sm:py-0 order-2 sm:order-2">
                      <Select
                        value={platform}
                        onValueChange={handlePlatformChange}
                      >
                        <SelectTrigger className="border-0 bg-transparent shadow-none h-auto p-0 focus:ring-0 w-full sm:w-32 text-gray-700 dark:text-gray-300">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-white dark:bg-gray-800 backdrop-blur-xl border-2 border-gray-200 dark:border-gray-700 shadow-2xl">
                          <SelectItem
                            value="youtube"
                            className="focus:bg-red-50 dark:focus:bg-red-950/30 hover:bg-red-50 dark:hover:bg-red-950/20"
                          >
                            <div className="flex items-center gap-3 py-2">
                              <div className="w-4 h-4 bg-red-600 rounded-full shadow-sm" />
                              <span className="font-medium text-gray-900 dark:text-white">
                                YouTube
                              </span>
                            </div>
                          </SelectItem>
                          <SelectItem
                            value="tiktok"
                            className="focus:bg-pink-50 dark:focus:bg-pink-950/30 hover:bg-pink-50 dark:hover:bg-pink-950/20"
                          >
                            <div className="flex items-center gap-3 py-2">
                              <div className="w-4 h-4 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full shadow-sm" />
                              <span className="font-medium text-gray-900 dark:text-white">
                                TikTok
                              </span>
                            </div>
                          </SelectItem>
                          <SelectItem
                            value="facebook"
                            className="focus:bg-blue-50 dark:focus:bg-blue-950/30 hover:bg-blue-50 dark:hover:bg-blue-950/20"
                          >
                            <div className="flex items-center gap-3 py-2">
                              <div className="w-4 h-4 bg-blue-600 rounded-full shadow-sm" />
                              <span className="font-medium text-gray-900 dark:text-white">
                                Facebook
                              </span>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Search Button - Mobile: full width, Desktop: compact */}
                    <div className="p-2 order-3 sm:order-3">
                      <Button
                        onClick={handleSearch}
                        disabled={loading || !keyword.trim()}
                        size="lg"
                        className="w-full sm:w-auto rounded-lg sm:rounded-xl px-6 sm:px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl hover:shadow-blue-500/25 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95"
                      >
                        {loading ? (
                          <>
                            <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 animate-spin mr-2" />
                            <span className="hidden sm:inline">
                              Searching...
                            </span>
                          </>
                        ) : (
                          <>
                            <Search className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                            Search
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Enhanced Quick Actions */}
                <div className="flex flex-col space-y-3 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
                  {/* Popular Searches with better mobile layout */}
                  <div className="flex flex-wrap items-center gap-2 justify-center sm:justify-start">
                    <span className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 font-medium whitespace-nowrap">
                      Try:
                    </span>
                    {["AI tools", "fitness", "crypto", "fashion", "food"].map(
                      (suggestion) => (
                        <Button
                          key={suggestion}
                          variant="outline"
                          size="sm"
                          className="h-7 sm:h-8 px-2 sm:px-3 text-xs rounded-full border-gray-300 dark:border-gray-600 hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-950/20 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-200 transform hover:scale-105 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300"
                          onClick={() => {
                            setKeyword(suggestion);
                            dispatch(
                              spyheroActions.setSearchKeyword(suggestion)
                            );
                          }}
                        >
                          {suggestion}
                        </Button>
                      )
                    )}
                  </div>

                  {/* Clear Results with better mobile styling */}
                  {ads.length > 0 && !loading && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleClearResults}
                      className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 transform hover:scale-105 self-center sm:self-auto"
                    >
                      Clear results
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Error Display */}
        {error && (
          <Alert
            variant="destructive"
            className="border-destructive/20 shadow-lg bg-destructive/5"
          >
            <AlertCircle className="h-5 w-5" />
            <AlertDescription className="text-base">{error}</AlertDescription>
          </Alert>
        )}

        {/* Results Summary */}
        {/* {(totalCount > 0 || lastCrawled) && !loading && (
          <Card className="border border-border/30 shadow-lg bg-gradient-to-r from-green-50/50 to-emerald-50/50 dark:from-green-950/20 dark:to-emerald-950/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-500/10 rounded-xl">
                  <BarChart3 className="w-6 h-6 text-green-600" />
                </div>
                <div className="flex-1">
                  <div className="font-bold text-xl text-foreground">
                    {totalCount.toLocaleString()} ads found
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Searching "{searchKeyword}" on {searchPlatform.charAt(0).toUpperCase() + searchPlatform.slice(1)}
                  </div>
                  {lastCrawled && (
                    <div className="text-sm text-muted-foreground flex items-center gap-2 mt-1">
                      <Clock className="w-4 h-4" />
                      Updated: {formatDate(lastCrawled)}
                    </div>
                  )}
                </div>
                <Badge variant="secondary" className="text-base px-4 py-2 bg-green-500/10 text-green-700 dark:text-green-400 border-green-500/20">
                  {totalCount.toLocaleString()} results
                </Badge>
              </div>
            </CardContent>
          </Card>
        )} */}

        {/* Improved Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12 sm:py-16 lg:py-24">
            <Card className="border border-gray-200 dark:border-gray-700 shadow-2xl p-6 sm:p-8 lg:p-12 bg-white dark:bg-gray-800 mx-4">
              <div className="text-center space-y-6 sm:space-y-8">
                {/* Enhanced loading animation */}
                <div className="relative">
                  <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto relative">
                    {/* Outer ring */}
                    <div className="absolute inset-0 border-4 border-blue-200 dark:border-blue-800 rounded-full"></div>
                    {/* Spinning ring */}
                    <div className="absolute inset-0 border-4 border-transparent border-t-blue-600 border-r-blue-600 rounded-full animate-spin"></div>
                    {/* Inner pulsing dot */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-3 h-3 bg-blue-600 rounded-full animate-pulse"></div>
                    </div>
                  </div>
                  {/* Searching text with typing effect */}
                  <div className="mt-4 sm:mt-6 space-y-2 sm:space-y-3">
                    <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
                      Analyzing {platform} ads...
                    </div>
                    <div className="text-gray-600 dark:text-gray-400 text-base sm:text-lg">
                      Searching for "{keyword}"
                    </div>
                    {/* Progress dots */}
                    <div className="flex justify-center space-x-2 mt-3 sm:mt-4">
                      <div
                        className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"
                        style={{ animationDelay: "0ms" }}
                      ></div>
                      <div
                        className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"
                        style={{ animationDelay: "150ms" }}
                      ></div>
                      <div
                        className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"
                        style={{ animationDelay: "300ms" }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Results Grid */}
        {ads.length > 0 && !loading && (
          <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {ads.map((ad) => renderAdCard(ad))}
          </div>
        )}

        {/* Enhanced No Results */}
        {!loading && !error && ads.length === 0 && searchKeyword && (
          <Card className="border border-border/30 shadow-xl mx-4 sm:mx-0">
            <CardContent className="py-12 sm:py-16 lg:py-20 text-center space-y-6 sm:space-y-8">
              <div className="relative">
                <Search className="w-16 h-16 sm:w-20 sm:h-20 mx-auto text-muted-foreground/40" />
              </div>
              <div className="space-y-3 sm:space-y-4">
                <h3 className="text-xl sm:text-2xl font-bold text-foreground">
                  No ads found
                </h3>
                <p className="text-muted-foreground max-w-md mx-auto text-base sm:text-lg px-4">
                  Try different keywords or switch platforms to discover more
                  ads.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row flex-wrap justify-center gap-2 sm:gap-3 px-4">
                <Button
                  variant="outline"
                  className="transition-all duration-200 hover:bg-muted/50"
                  onClick={() =>
                    setPlatform(platform === "youtube" ? "tiktok" : "youtube")
                  }
                >
                  Try {platform === "youtube" ? "TikTok" : "YouTube"}
                </Button>
                <Button
                  variant="outline"
                  className="transition-all duration-200 hover:bg-muted/50"
                  onClick={() => setKeyword("marketing")}
                >
                  Search "marketing"
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Enhanced Empty State */}
        {!loading && !error && ads.length === 0 && !searchKeyword && (
          <Card className="border border-border/30 shadow-xl bg-gradient-to-br from-card to-muted/5">
            <CardContent className="py-24 text-center space-y-10">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-purple-500/10 rounded-full blur-2xl"></div>
                <Filter className="w-24 h-24 mx-auto text-primary/60 relative" />
              </div>

              <div className="space-y-6">
                <h3 className="text-3xl font-bold text-foreground">
                  Start Your Ad Intelligence Journey
                </h3>
                <p className="text-muted-foreground max-w-2xl mx-auto text-lg leading-relaxed">
                  Uncover winning ad strategies, analyze competitor campaigns,
                  and discover what's working in your industry.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 max-w-5xl mx-auto">
                <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-blue-50/30 to-indigo-50/30 dark:from-blue-950/10 dark:to-indigo-950/10 border border-blue-200/20 dark:border-blue-800/20 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                  <Eye className="w-12 h-12 mx-auto text-blue-600 mb-4" />
                  <h4 className="font-bold text-lg mb-3 text-foreground">
                    Discover Trends
                  </h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Find what's working in your industry with real-time data
                  </p>
                </div>

                <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-green-50/30 to-emerald-50/30 dark:from-green-950/10 dark:to-emerald-950/10 border border-green-200/20 dark:border-green-800/20 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                  <BarChart3 className="w-12 h-12 mx-auto text-green-600 mb-4" />
                  <h4 className="font-bold text-lg mb-3 text-foreground">
                    Performance Analytics
                  </h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Get detailed metrics and engagement insights
                  </p>
                </div>

                <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-purple-50/30 to-violet-50/30 dark:from-purple-950/10 dark:to-violet-950/10 border border-purple-200/20 dark:border-purple-800/20 transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
                  <Target className="w-12 h-12 mx-auto text-purple-600 mb-4" />
                  <h4 className="font-bold text-lg mb-3 text-foreground">
                    Competitive Intelligence
                  </h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Stay ahead with competitor analysis tools
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default SearchAd;

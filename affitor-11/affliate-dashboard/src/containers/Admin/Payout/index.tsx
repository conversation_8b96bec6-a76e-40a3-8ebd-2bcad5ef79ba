"use client";
import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  actions as payoutActions,
  selectAdminPayouts,
  selectAdminPayoutsLoading,
  selectAdminPayoutsError,
  selectAdminPayoutsMeta,
  selectSelectedPayouts,
} from "@/features/payout/payout.slice";
import { AppDispatch } from "@/store";
import PendingPayoutTable from "./PendingPayoutTable";
import ApprovedPayoutTable from "./ApprovedPayoutTable";
import CompletedPayoutTable from "./CompletedPayoutTable";
import PayoutFormModal from "@/components/Modals/PayoutFormModal";

type PayoutTab = "pending" | "approved" | "completed";

export default function AdminPayout() {
  const dispatch = useDispatch<AppDispatch>();
  const payouts = useSelector(selectAdminPayouts);
  const loading = useSelector(selectAdminPayoutsLoading);
  const error = useSelector(selectAdminPayoutsError);
  const meta = useSelector(selectAdminPayoutsMeta);
  const selectedPayouts = useSelector(selectSelectedPayouts);

  const [activeTab, setActiveTab] = useState<PayoutTab>("pending");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [mounted, setMounted] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  // Fix hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch payouts when tab changes or component mounts
  useEffect(() => {
    if (mounted) {
      dispatch(
        payoutActions.fetchAdminPayoutsRequest({
          payout_status:
            activeTab === "pending"
              ? "pending"
              : activeTab === "approved"
              ? "approved"
              : "completed",
          page: currentPage,
          pageSize: 10,
          search: searchTerm,
        })
      );
    }
  }, [dispatch, activeTab, currentPage, mounted]);

  // Handle search with debounce
  useEffect(() => {
    if (!mounted) return;

    const timer = setTimeout(() => {
      setCurrentPage(1);
      dispatch(
        payoutActions.fetchAdminPayoutsRequest({
          payout_status:
            activeTab === "pending"
              ? "pending"
              : activeTab === "approved"
              ? "approved"
              : "completed",
          page: 1,
          pageSize: 10,
          search: searchTerm,
        })
      );
    }, 500);

    return () => clearTimeout(timer);
  }, [dispatch, searchTerm, activeTab, mounted]);

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handleApprove = (documentId: string) => {
    dispatch(payoutActions.approvePayoutRequest(documentId));
  };

  const handleMarkAsPaid = (documentIds: string | string[]) => {
    dispatch(payoutActions.markAsPaidRequest(documentIds));
  };

  const handleToggleSelection = (documentId: string) => {
    dispatch(payoutActions.togglePayoutSelection(documentId));
  };

  const handleSelectAll = (documentIds: string[]) => {
    dispatch(payoutActions.selectAllPayouts(documentIds));
  };

  const handleClearSelection = () => {
    dispatch(payoutActions.clearPayoutSelection());
  };

  const handleExport = (format: "csv" | "excel") => {
    dispatch(
      payoutActions.exportPayoutsRequest({
        payout_status: activeTab,
        format,
        search: searchTerm,
      })
    );
  };

  const handleArchive = (documentId: string) => {
    dispatch(payoutActions.archivePayoutRequest(documentId));
  };

  const handlePayoutCreated = () => {
    // Refresh the current tab's data after successful payout creation
    dispatch(
      payoutActions.fetchAdminPayoutsRequest({
        payout_status:
          activeTab === "pending"
            ? "pending"
            : activeTab === "approved"
            ? "approved"
            : "completed",
        page: currentPage,
        pageSize: 10,
        search: searchTerm,
      })
    );
  };

  if (!mounted) {
    return null;
  }

  // Filter payouts based on active tab
  const filteredPayouts = (payouts || []).filter((payout: any) => {
    if (activeTab === "pending") return payout.payout_status === "pending";
    if (activeTab === "approved") return payout.payout_status === "approved";
    if (activeTab === "completed") return payout.payout_status === "completed";
    return false;
  });

  // Get counts for each tab
  const pendingCount = (payouts || []).filter(
    (p: any) => p.payout_status === "pending"
  ).length;
  const approvedCount = (payouts || []).filter(
    (p: any) => p.payout_status === "approved"
  ).length;
  const completedCount = (payouts || []).filter(
    (p: any) => p.payout_status === "completed"
  ).length;

  return (
    <div className="p-3 sm:p-4 lg:p-6 bg-gray-50 dark:bg-gray-900 min-h-full">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Payouts
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Payouts are automatically generated on a NET-15 basis, meaning 15
              days after the end of each month.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <button className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
              Upload payouts
            </button>

            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="flex justify-center items-center bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Generate payouts
            </button>
            
          </div>
        </div>
      </div>

      {/* Info Box */}
      <div className="mb-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
          What are Payouts?
        </h3>
        <p className="text-sm text-blue-700 dark:text-blue-300">
          Payouts are automatically generated on a NET-15 basis, meaning 15 days
          after the end of each month. For example, commissions earned in
          September will have payouts generated on October 15th. You can also
          manually generate payouts at any time using the "Generate Payouts"
          button.{" "}
          <a href="#" className="underline hover:no-underline">
            Learn more
          </a>
        </p>
      </div>

      {/* Tabs */}
      <div className="mb-6">
        <nav className="flex space-x-1 bg-white dark:bg-gray-800 rounded-lg p-1 shadow-sm border border-gray-200 dark:border-gray-700 overflow-x-auto">
          {[
            { key: "pending", label: "Pending", count: pendingCount },
            { key: "approved", label: "Approved", count: approvedCount },
            { key: "completed", label: "Paid", count: completedCount },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as PayoutTab)}
              className={`flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors whitespace-nowrap ${
                activeTab === tab.key
                  ? "bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400"
                  : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Search Bar */}
      <div className="mb-6 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="relative">
          <svg
            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
          <input
            type="text"
            placeholder="Search by partner name, email, or payout ID..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2.5 text-sm border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
          />
        </div>
      </div>

      {/* Error State */}
      {error && (
        <div className="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg text-sm">
          {error}
        </div>
      )}

      {/* Tab Content */}
      {activeTab === "pending" && (
        <PendingPayoutTable
          payouts={filteredPayouts}
          loading={loading}
          error={error}
          meta={meta}
          currentPage={currentPage}
          onPageChange={handlePageChange}
          onApprove={handleApprove}
        />
      )}
      {activeTab === "approved" && (
        <ApprovedPayoutTable
          payouts={filteredPayouts}
          loading={loading}
          error={error}
          meta={meta}
          currentPage={currentPage}
          onPageChange={handlePageChange}
          onMarkAsPaid={handleMarkAsPaid}
          selectedPayouts={selectedPayouts}
          onToggleSelection={handleToggleSelection}
          onSelectAll={handleSelectAll}
          onClearSelection={handleClearSelection}
          onExport={handleExport}
        />
      )}
      {activeTab === "completed" && (
        <CompletedPayoutTable
          payouts={filteredPayouts}
          loading={loading}
          error={error}
          meta={meta}
          currentPage={currentPage}
          onPageChange={handlePageChange}
          onExport={handleExport}
          onArchive={handleArchive}
        />
      )}

      {/* Create Payout Modal */}
      <PayoutFormModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handlePayoutCreated}
      />
    </div>
  );
}

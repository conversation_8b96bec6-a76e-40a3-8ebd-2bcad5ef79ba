import { FilterState, ICategory, ITag } from "@/interfaces";
import { RootState } from "@/store";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { createSelector } from "reselect";

// Define the complete filter state structure
interface FilterSliceState {
  filters: FilterState;
  activeCategory: ICategory | null;
  activeTag: ITag | null;
  loading: boolean;
  error: string | null;
  isHashInitialized: boolean;
}

// Default filters with empty values
const defaultFilters: FilterState = {
  pricing: { from: "", to: "" },
  commission: { from: "", to: "" },
  conversion: { from: "", to: "" },
  monthlyTraffic: { from: "", to: "" },
  cookiesDuration: { from: "", to: "" },
  category: "",
  paymentMethod: "",
  recurring: "",
  countries: [],
  launchYears: [],
};

// Initial state
const initialState: FilterSliceState = {
  filters: defaultFilters,
  activeCategory: null,
  activeTag: null,
  loading: false,
  error: null,
  isHashInitialized: false,
};

const filterSlice = createSlice({
  name: "filter",
  initialState,
  reducers: {
    // Set entire filter state (e.g., from URL hash)
    setFilters: (state, action: PayloadAction<FilterState>) => {
      state.filters = action.payload;
    },
    
    // Set active category
    setActiveCategory: (state, action: PayloadAction<ICategory | null>) => {
      state.activeCategory = action.payload;
      // Don't update category ID in filter state anymore - category is handled by URL path
      // state.filters.category = action.payload?.slug || "";
    },
    
    // Set active tag
    setActiveTag: (state, action: PayloadAction<ITag | null>) => {
      state.activeTag = action.payload;
    },
    
    // Update individual filter fields
    updateFilter: (
      state,
      action: PayloadAction<{
        key: keyof FilterState;
        value: any;
      }>
    ) => {
      const { key, value } = action.payload;
      if (Array.isArray(state.filters[key])) {
        // For array fields like countries, launchYears
        (state.filters as any)[key] = value;
      } else if (typeof value === "object") {
        // For range filters like pricing, commission, etc.
        const currentValue = state.filters[key as keyof FilterState] as Record<string, any>;
        state.filters[key as keyof FilterState] = { ...currentValue, ...value };
      } else {
        // For simple string values like category, paymentMethod, etc.
        (state.filters as any)[key] = value;
      }
    },
    
    // Reset all filters to default
    resetFilters: (state) => {
      state.filters = defaultFilters;
      state.activeCategory = null;
      state.activeTag = null;
    },
    
    // Set hash initialization status
    setHashInitialized: (state, action: PayloadAction<boolean>) => {
      state.isHashInitialized = action.payload;
    },
    
    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    
    // Set error state
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    // Set default category to "All Programs"
    setDefaultCategory: (state, action: PayloadAction<ICategory[]>) => {
      // Find the "All Programs" category
      const allProgramsCategory = action.payload.find(cat => cat.name === "All Programs" || cat.id === "");
      
      if (allProgramsCategory) {
        state.activeCategory = allProgramsCategory;
        state.filters.category = allProgramsCategory.id || "";
      }
    },
  },
});

export const { actions, reducer } = filterSlice;

// Selectors
const selectFilterState = (state: RootState) => state.filter;

export const selectFilters = createSelector(
  [selectFilterState],
  (filterState) => filterState.filters
);

export const selectActiveCategory = createSelector(
  [selectFilterState],
  (filterState) => filterState.activeCategory
);

export const selectActiveTag = createSelector(
  [selectFilterState],
  (filterState) => filterState.activeTag
);

export const selectIsHashInitialized = createSelector(
  [selectFilterState],
  (filterState) => filterState.isHashInitialized
);

export const selectFilterLoading = createSelector(
  [selectFilterState],
  (filterState) => filterState.loading
);

export const selectFilterError = createSelector(
  [selectFilterState],
  (filterState) => filterState.error
);

// Helper selector to count active filters
export const selectActiveFilterCount = createSelector(
  [selectFilters],
  (filters) => {
    let count = 0;
    if (filters.pricing.from || filters.pricing.to) count++;
    if (filters.commission.from || filters.commission.to) count++;
    if (filters.conversion.from || filters.conversion.to) count++;
    if (filters.monthlyTraffic.from || filters.monthlyTraffic.to) count++;
    if (filters.cookiesDuration.from || filters.cookiesDuration.to) count++;
    // if (filters.category) count++;
    if (filters.paymentMethod) count++;
    if (filters.recurring) count++;
    return count;
  }
);

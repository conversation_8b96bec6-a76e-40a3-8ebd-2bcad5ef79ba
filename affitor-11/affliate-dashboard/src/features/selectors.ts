import {
  selectAffiliatePrograms,
  selectLoadingAffiliatePrograms,
  selectCurrentAffiliate,
  selectAffiliateUrl,
  selectAffiliatePagination,
  selectErrorAffiliate,
  selectLoadingAffiliateUrl,
  selectLoadingAffiliateUrlId,
} from "./affiliate/affiliate.slice";
import {
  selectCategories,
  selectFilterCategory,
  selectCategoryLoading,
  selectCategoryError,
} from "./category/category.slice";
import {
  selectSocialListeningCurrent,
  selectSocialListeningLoading,
  selectSocialListeningList,
  selectSocialListeningPagination,
  selectTranscript,
  selectLoadingTranscript,
  selectLoadingTranscriptId,
} from "./social-listening/social-listening.slice";
import {
  selectAiscriptMessages,
  selectAiscriptLoading,
  selectAiscriptOpen,
  selectAiscriptError,
  selectAiscriptQuickReplies,
} from "./aiscript/aiscript.slice";
import {
  selectPaymentMethods,
  selectPaymentMethodsLoading,
  selectPaymentMethodsError,
  selectPaymentMethodsPagination,
} from "./payment-method/payment-method.slice";
import {
  selectUserData,
  selectUserLoading,
  selectUserError,
  selectUserIsPremium,
  selectUserIsUpdating,
  selectAdminUsers,
  selectAdminUsersLoading,
  selectAdminUsersError,
  selectAdminUsersPagination,
} from "./user/user.slice";
import {
  selectSubscriptionTiers,
  selectSubscriptionTiersLoading,
  selectSubscriptionTiersError,
  selectSubscriptionTiersMeta,
  selectCurrentSubscription,
  selectComparisonPlans,
  selectComparisonPlansLoading,
  selectComparisonPlansError,
} from "./subscription-tier/subscription-tier.slice";
import {
  selectTopVideosList,
  selectTopVideosLoading,
  selectTopVideosPagination,
  selectTopVideosError,
} from "./top-videos/top-videos.slice";
import {
  selectTopAdsList,
  selectTopAdsLoading,
  selectTopAdsPagination,
  selectTopAdsError,
} from "./top-ads/top-ads.slice";
import { selectIsAuthenticated } from "./auth/auth.slice";
import {
  selectReferrerLoading,
  selectReferrerError,
  selectReferrerSuccess,
} from "./referrer/referrer.slice";
import {
  selectReferrerLinks,
  selectReferrerLinksLoading,
  selectReferrerLinksError,
  selectReferrerLinksPagination,
} from "./referrer-links/referrer-links.slice";
import {
  selectReferralCommissions,
  selectReferralCommissionsLoading,
  selectReferralCommissionsError,
  selectReferralCommissionsPagination,
  selectReferralCommissionStats,
  selectReferralCommissionStatsLoading,
} from "./referral-commission/referral-commission.slice";
import {
  selectReferrals,
  selectReferralsLoading,
  selectReferralsError,
  selectReferralsPagination,
} from "./referral/referral.slice";
import {
  selectPayoutOverview,
  selectPayoutLoading,
  selectPayoutError,
  selectPayoutHistory,
  selectPayoutHistoryMeta,
  selectPayoutHistoryLoading,
  selectPayoutHistoryError,
} from "./payout/payout.slice";
import {
  selectAdminIsAuthenticated,
  selectAdminLoading,
  selectAdminError,
  selectAdminData,
  selectAdminToken,
  selectAdminPartners,
  selectAdminPartnersLoading,
  selectAdminPartnersError,
  selectAdminPartnersPagination,
  selectAdminDashboardStats,
  selectAdminDashboardStatsLoading,
  selectAdminDashboardStatsError,
} from "./admin/admin.slice";
import {
  selectSpyHeroAds,
  selectSpyHeroLoading,
  selectSpyHeroError,
  selectSpyHeroSearchKeyword,
  selectSpyHeroSearchPlatform,
  selectSpyHeroTotalCount,
  selectSpyHeroLastCrawled,
} from "./spyhero/spyhero.slice";
import {
  selectTransactions,
  selectTransactionsLoading,
  selectTransactionsError,
  selectTransactionsPagination,
} from "./transaction/transaction.slice";

// Track Links selectors
const selectTrackLinksOverview = (state: any) => state.trackLinks.overview;
const selectTrackLinksPerformanceOverview = (state: any) =>
  state.trackLinks.performanceOverview;
const selectTrackLinksLoading = (state: any) => state.trackLinks.loading;
const selectTrackLinksPerformanceLoading = (state: any) =>
  state.trackLinks.performanceLoading;
const selectTrackLinksError = (state: any) => state.trackLinks.error;

// Referral Activity selectors
const selectReferralActivities = (state: any) =>
  state.referralActivity.activities;
const selectReferralActivityLoading = (state: any) =>
  state.referralActivity.loading;
const selectReferralActivityError = (state: any) =>
  state.referralActivity.error;
const selectReferralActivityMeta = (state: any) => state.referralActivity.meta;

export {
  // Auth selectors
  selectIsAuthenticated,

  // Affiliate selectors
  selectAffiliatePrograms,
  selectLoadingAffiliatePrograms,
  selectCurrentAffiliate,
  selectAffiliateUrl,
  selectAffiliatePagination,
  selectErrorAffiliate,
  selectLoadingAffiliateUrl,
  selectLoadingAffiliateUrlId,

  // Category selectors
  selectCategories,
  selectFilterCategory,
  selectCategoryLoading,
  selectCategoryError,

  // Social Listening selectors
  selectSocialListeningCurrent,
  selectSocialListeningLoading,
  selectSocialListeningList,
  selectSocialListeningPagination,
  selectTranscript,
  selectLoadingTranscript,
  selectLoadingTranscriptId,

  // AI Script selectors
  selectAiscriptMessages,
  selectAiscriptLoading,
  selectAiscriptOpen,
  selectAiscriptError,
  selectAiscriptQuickReplies,

  // Payment Method selectors
  selectPaymentMethods,
  selectPaymentMethodsLoading,
  selectPaymentMethodsError,
  selectPaymentMethodsPagination,

  // User selectors
  selectUserData,
  selectUserLoading,
  selectUserError,
  selectUserIsPremium,
  selectUserIsUpdating,
  selectAdminUsers,
  selectAdminUsersLoading,
  selectAdminUsersError,
  selectAdminUsersPagination,

  // Subscription Tier selectors
  selectSubscriptionTiers,
  selectSubscriptionTiersLoading,
  selectSubscriptionTiersError,
  selectSubscriptionTiersMeta,
  selectCurrentSubscription,
  selectComparisonPlans,
  selectComparisonPlansLoading,
  selectComparisonPlansError,

  // Top Videos selectors
  selectTopVideosList,
  selectTopVideosLoading,
  selectTopVideosPagination,
  selectTopVideosError,

  // Top Ads selectors
  selectTopAdsList,
  selectTopAdsLoading,
  selectTopAdsPagination,
  selectTopAdsError,

  // Referrer selectors
  selectReferrerLoading,
  selectReferrerError,
  selectReferrerSuccess,

  // Referrer Links selectors
  selectReferrerLinks,
  selectReferrerLinksLoading,
  selectReferrerLinksError,
  selectReferrerLinksPagination,

  // Track Links selectors
  selectTrackLinksOverview,
  selectTrackLinksPerformanceOverview,
  selectTrackLinksLoading,
  selectTrackLinksPerformanceLoading,
  selectTrackLinksError,

  // Referral Activity selectors
  selectReferralActivities,
  selectReferralActivityLoading,
  selectReferralActivityError,
  selectReferralActivityMeta,

  // Referral Commission selectors
  selectReferralCommissions,
  selectReferralCommissionsLoading,
  selectReferralCommissionsError,
  selectReferralCommissionsPagination,
  selectReferralCommissionStats,
  selectReferralCommissionStatsLoading,

  // Referral selectors
  selectReferrals,
  selectReferralsLoading,
  selectReferralsError,
  selectReferralsPagination,

  // Payout selectors
  selectPayoutOverview,
  selectPayoutLoading,
  selectPayoutError,
  selectPayoutHistory,
  selectPayoutHistoryMeta,
  selectPayoutHistoryLoading,
  selectPayoutHistoryError,

  // Admin selectors
  selectAdminIsAuthenticated,
  selectAdminLoading,
  selectAdminError,
  selectAdminData,
  selectAdminToken,
  selectAdminPartners,
  selectAdminPartnersLoading,
  selectAdminPartnersError,
  selectAdminPartnersPagination,
  selectAdminDashboardStats,
  selectAdminDashboardStatsLoading,
  selectAdminDashboardStatsError,
  // SpyHero selectors
  selectSpyHeroAds,
  selectSpyHeroLoading,
  selectSpyHeroError,
  selectSpyHeroSearchKeyword,
  selectSpyHeroSearchPlatform,
  selectSpyHeroTotalCount,
  selectSpyHeroLastCrawled,

  // Transaction selectors
  selectTransactions,
  selectTransactionsLoading,
  selectTransactionsError,
  selectTransactionsPagination,
};

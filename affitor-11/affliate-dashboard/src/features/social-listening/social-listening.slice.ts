import { IPagination, IVideo, ISort } from "@/interfaces";
import { createSelector } from "reselect";
import { RootState } from "@/store";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
const selectSocialListeningState = (state: RootState) => state.socialListening;

interface SocialListeningState {
  list: IVideo[] | null;
  loading: boolean;
  pagination?: IPagination;
  current?: IVideo | null;
  error?: string | null;
  transcript?: string | null;
  loadingTranscript: boolean;
  loadingTranscriptId: string | null; // Add this line to track which videoId is being loaded
}

const initialState: SocialListeningState = {
  list: null,
  loading: false,
  current: null,
  error: null,
  transcript: null,
  loadingTranscript: false,
  loadingTranscriptId: null, // Initialize as null
};

const socialListeningSlice = createSlice({
  name: "socialListening",
  initialState,
  reducers: {
    // Sync actions
    setSocialListenings: (state, action: PayloadAction<IVideo[] | null >) => {
      if (state.list && state.list.length && action.payload) {
        const ids = new Set(state.list.map((item) => item.documentId));
        state.list = [
          ...state.list,
          ...action.payload.filter((item) => !ids.has(item.documentId)),
        ];
      } else {
        state.list = action.payload;
      }
    },

    fetch: (
      state,
      action: PayloadAction<{
        platforms: string[];
        affiliateDocId: string;
        pagination: IPagination;
        sort: ISort
      }>
    ) => {},

    setSocialListening: (state, action: PayloadAction<IVideo>) => {
      state.current = action.payload;
    },

    setPagination: (state, action: PayloadAction<IPagination>) => {
      state.pagination = action.payload;
    },

    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },

    setError(state, action: PayloadAction<string | null>) {
      state.error = action.payload;
    },

    // New actions for transcript
    fetchTranscript(state, action: PayloadAction<string>) {
      state.loadingTranscript = true;
      state.loadingTranscriptId = action.payload; // Store the videoId being loaded
    },

    setTranscript(state, action: PayloadAction<string | null>) {
      state.transcript = action.payload;
      state.loadingTranscript = false;
      state.loadingTranscriptId = null; // Clear the videoId when loading is complete
    },

    setLoadingTranscript(state, action: PayloadAction<boolean>) {
      state.loadingTranscript = action.payload;
      if (!action.payload) {
        state.loadingTranscriptId = null; // Clear the videoId if setting loading to false
      }
    },
  },
});

export const { actions, reducer } = socialListeningSlice;

// social listening
export const selectSocialListeningList = createSelector(
  [selectSocialListeningState],
  (socialListeningState) => socialListeningState.list
);

export const selectSocialListeningLoading = createSelector(
  [selectSocialListeningState],
  (socialListeningState) => socialListeningState.loading
);

export const selectSocialListeningCurrent = createSelector(
  [selectSocialListeningState],
  (socialListeningState) => socialListeningState.current
);

export const selectSocialListeningPagination = createSelector(
  [selectSocialListeningState],
  (socialListeningState) => socialListeningState.pagination
);

// New selectors for transcript
export const selectTranscript = createSelector(
  [selectSocialListeningState],
  (socialListeningState) => socialListeningState.transcript
);

export const selectLoadingTranscript = createSelector(
  [selectSocialListeningState],
  (socialListeningState) => socialListeningState.loadingTranscript
);

// Add a selector for loadingTranscriptId
export const selectLoadingTranscriptId = createSelector(
  [selectSocialListeningState],
  (socialListeningState) => socialListeningState.loadingTranscriptId
);

// New selector for transcript text specifically - this will be used by AIScript
export const selectTranscriptText = createSelector(
  [selectSocialListeningState],
  (socialListeningState) => socialListeningState.transcript
);
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export interface TrackLinksOverview {
  visitors: {
    today: number;
    yesterday: number;
    difference: number;
    percentage: number;
    trend: "increase" | "decrease" | "stable";
    total: number;
  };
  conversions: {
    today: number;
    yesterday: number;
    difference: number;
    percentage: number;
    trend: "increase" | "decrease" | "stable";
    total: number;
  };
  leads: {
    today: number;
    yesterday: number;
    difference: number;
    percentage: number;
    trend: "increase" | "decrease" | "stable";
    total: number;
  };
  total: {
    today: number;
    yesterday: number;
    difference: number;
    percentage: number;
    trend: "increase" | "decrease" | "stable";
    total: number;
  };
}

export interface TrackLinksPerformanceOverview {
  time: string[];
  visitors: number[];
  leads: number[];
  conversions: number[];
}

interface TrackLinksState {
  overview: TrackLinksOverview | null;
  performanceOverview: TrackLinksPerformanceOverview | null;
  loading: boolean;
  performanceLoading: boolean;
  error: string | null;
}

const initialState: TrackLinksState = {
  overview: null,
  performanceOverview: null,
  loading: false,
  performanceLoading: false,
  error: null,
};

const trackLinksSlice = createSlice({
  name: "trackLinks",
  initialState,
  reducers: {
    // Overview actions
    fetchOverviewRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchOverviewSuccess: (
      state,
      action: PayloadAction<TrackLinksOverview>
    ) => {
      state.loading = false;
      state.overview = action.payload;
      state.error = null;
    },
    fetchOverviewFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    // Performance Overview actions
    fetchPerformanceOverviewRequest: (
      state,
      action: PayloadAction<{ period: string }>
    ) => {
      state.performanceLoading = true;
      state.error = null;
    },
    fetchPerformanceOverviewSuccess: (
      state,
      action: PayloadAction<TrackLinksPerformanceOverview>
    ) => {
      state.performanceLoading = false;
      state.performanceOverview = action.payload;
      state.error = null;
    },
    fetchPerformanceOverviewFailure: (state, action: PayloadAction<string>) => {
      state.performanceLoading = false;
      state.error = action.payload;
    },
    // Clear state
    clearTrackLinksState: (state) => {
      state.overview = null;
      state.performanceOverview = null;
      state.error = null;
      state.loading = false;
      state.performanceLoading = false;
    },
  },
});

export const { actions } = trackLinksSlice;
export const { reducer } = trackLinksSlice;
export default trackLinksSlice.reducer;

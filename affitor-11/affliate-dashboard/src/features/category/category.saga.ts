import { call, put, takeEvery, select } from "redux-saga/effects";
import { actions } from "./category.slice";
import { actions as filterActions } from "@/features/filter/filter.slice";
import { ICategory } from "@/interfaces";
import { PayloadAction } from "@reduxjs/toolkit";

function* handleFetch(): Generator<any, void, any> {
  try {
    yield put(actions.setLoadingCategory(true));
    yield put(actions.setError(null));
    
    const response: any = yield call(fetch, "/api/categories");
    
    if (!response.ok) {
      const errorMsg = `Failed to fetch categories: ${response.status} ${response.statusText}`;
      console.error(errorMsg);
      yield put(actions.setError(errorMsg));
      return;
    }
    
    const { data } = yield response.json();
    
    // Ensure we have the "All Programs" category at the beginning with slug
    const allCategory = {
      id: "",
      documentId: "",
      name: "All Programs",
      slug: ""
    };
    
    // Check if "All Programs" is already in the data
    const allProgramsExists = data.some((cat: ICategory) => 
      cat.name === "All Programs" || cat.slug === "" || cat.id === "");
    
    // Add "All Programs" to the beginning if it doesn't exist
    const categoriesWithAll = allProgramsExists 
      ? data 
      : [allCategory, ...data];
    
    yield put(actions.setCategories(categoriesWithAll));
    
    // After loading categories, check if there's a category in the filters
    // that needs to be matched with a full category object
    const filters = yield select(state => state.filter.filters);
    
    if (filters && filters.category) {
      // Try to find the category object that matches the slug in filters
      const matchingCategory = categoriesWithAll.find(
        (cat: ICategory) => cat.slug === filters.category
      );
      
      if (matchingCategory) {
        console.log("Found matching category for filter slug:", matchingCategory.name);
        
        // Set both the filter's active category and the current category
        yield put(filterActions.setActiveCategory(matchingCategory));
        yield put(actions.setCurrentCategory(matchingCategory));
      }
    } else {
      // If no category ID in filters, check if we need to set the default
      const activeCategory = yield select(state => state.filter.activeCategory);
      
      if (!activeCategory) {
        yield put(filterActions.setDefaultCategory(categoriesWithAll));
      }
    }
  } catch (error: any) {
    yield put(actions.setError("Failed to fetch categories"));
  } finally {
    yield put(actions.setLoadingCategory(false));
  }
}

function* handleSetCategoryById(action: PayloadAction<string>): Generator<any, void, any> {
  try {
    const categoryId = action.payload;
    yield put(actions.setLoadingCategory(true));
    
    if (!categoryId) {
      // If no id provided, set the "All Programs" category
      const allPrograms = {
        id: "",
        documentId: "",
        name: "All Programs",
        slug: ""
      };
      
      // Update both states for consistency
      yield put(actions.setCurrentCategory(allPrograms));
      yield put(filterActions.setActiveCategory(allPrograms));
      
      return;
    }
    
    // Get the current categories list
    const categories: ICategory[] = yield select(state => state.category.list);
    
    // Find the matching category by ID
    const matchingCategory = categories.find((cat: ICategory) => cat.id === categoryId);

    console.log("LOG-matchingCategory", matchingCategory);
    
    if (matchingCategory) {
      console.log("Setting category by ID: Found match for", categoryId);
      
      // Set in both states for consistency
      yield put(actions.setCurrentCategory(matchingCategory));
      yield put(filterActions.setActiveCategory(matchingCategory));
    } else {
      console.warn(`Category with ID ${categoryId} not found in current list. Will try again when categories load.`);
      
      // If no match found (possibly because categories haven't loaded yet)
      // Store the ID in the filter state for later matching
      yield put(filterActions.updateFilter({
        key: "category",
        value: categoryId
      }));
      
      // Default to "All Programs" for now
      yield put(actions.setCurrentCategory({
        id: "",
        documentId: "",
        name: "All Programs",
        slug: ""
      }));
    }
  } catch (error: any) {
    console.error("Error setting category by ID:", error);
    yield put(actions.setError("Failed to set category"));
  } finally {
    yield put(actions.setLoadingCategory(false));
  }
}

function* handleSetCategoryBySlug(action: PayloadAction<string>): Generator<any, void, any> {
  try {
    const categorySlug = action.payload;
    yield put(actions.setLoadingCategory(true));
    
    if (!categorySlug) {
      // If no slug provided, set the "All Programs" category
      const allPrograms = {
        id: "",
        documentId: "",
        name: "All Programs",
        slug: ""
      };
      
      // Update both states for consistency
      yield put(actions.setCurrentCategory(allPrograms));
      yield put(filterActions.setActiveCategory(allPrograms));
      
      return;
    }
    
    // Get the current categories list
    const categories: ICategory[] = yield select(state => state.category.list);
    
    // Find the matching category by slug
    const matchingCategory = categories.find((cat: ICategory) => cat.slug === categorySlug);
    
    if (matchingCategory) {
      console.log("Setting category by slug: Found match for", categorySlug);
      
      // Set in both states for consistency
      yield put(actions.setCurrentCategory(matchingCategory));
      yield put(filterActions.setActiveCategory(matchingCategory));
    } else {
      console.warn(`Category with slug "${categorySlug}" not found in current list. Will try again when categories load.`);
      
      // If no match found (possibly because categories haven't loaded yet)
      // Store the slug in the filter state for later matching
      yield put(filterActions.updateFilter({
        key: "category",
        value: categorySlug
      }));
      
      // Default to "All Programs" for now
      yield put(actions.setCurrentCategory({
        id: "",
        documentId: "",
        name: "All Programs",
        slug: ""
      }));
    }
  } catch (error: any) {
    console.error("Error setting category by slug:", error);
    yield put(actions.setError("Failed to set category"));
  } finally {
    yield put(actions.setLoadingCategory(false));
  }
}

export default function* categorySaga() {
  yield takeEvery(actions.fetchAll.type, handleFetch);
  yield takeEvery(actions.setCategoryById.type, handleSetCategoryById);
  yield takeEvery(actions.setCategoryBySlug.type, handleSetCategoryBySlug);
}

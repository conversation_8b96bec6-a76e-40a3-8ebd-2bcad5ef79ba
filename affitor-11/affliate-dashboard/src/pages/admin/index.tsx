"use client";
import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { selectAdminIsAuthenticated, selectAdminLoading, selectAdminError } from "@/features/selectors";
import { adminActions } from "@/features/rootActions";
import AdminAuthentication from "@/containers/Admin/Authentication";
import AdminContainer from "@/containers/Admin";

export default function AdminPage() {
  const dispatch = useDispatch();
  const isAuthenticated = useSelector(selectAdminIsAuthenticated);
  const isLoading = useSelector(selectAdminLoading);
  const error = useSelector(selectAdminError);
  const [mounted, setMounted] = useState(false);
  const [initialCheckDone, setInitialCheckDone] = useState(false);

  // Fix hydration mismatch by ensuring client-side rendering
  useEffect(() => {
    setMounted(true);
  }, []);

  // Initial authentication check
  useEffect(() => {
    if (!mounted) return;

    const token = typeof window !== "undefined" ? localStorage.getItem("admin_token") : null;
    
    if (token && !isAuthenticated && !initialCheckDone) {
      // We have a token but Redux doesn't know about it, validate it
      dispatch(adminActions.checkAuthStatus());
    }
    
    setInitialCheckDone(true);
  }, [mounted, isAuthenticated, initialCheckDone, dispatch]);

  // Handle authentication check failure - clear any invalid tokens
  useEffect(() => {
    if (error && error.includes("expired") || error && error.includes("invalid")) {
      // Clear any stored tokens on authentication failure
      if (typeof window !== "undefined") {
        localStorage.removeItem("admin_token");
        localStorage.removeItem("admin_data");
      }
    }
  }, [error]);

  // Don't render anything until mounted to prevent hydration mismatches
  if (!mounted || !initialCheckDone) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Show loading while checking authentication
  if (isLoading && !isAuthenticated) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verifying admin credentials...</p>
        </div>
      </div>
    );
  }

  return isAuthenticated ? <AdminContainer /> : <AdminAuthentication />;
}

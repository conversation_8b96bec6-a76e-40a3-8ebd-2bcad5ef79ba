import { AppError } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<any | AppError>
) {
  const { method } = req;

  if (method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    // Get token from Authorization header
    // const authHeader = req.headers.authorization;
    // let token = null;

    // if (authHeader && authHeader.startsWith("Bearer ")) {
    //   token = authHeader.split(" ")[1];
    // }

    // Call StrapiClient to get comparison plans
    const comparePlansData = await StrapiClient.getComparisonPlans();
    return res.status(200).json(comparePlansData);
  } catch (error: any) {
    console.error("Error in /api/subscription-tiers/compare-plans:", error);
    sendApiError(res, error, "Error fetching comparison plans data");
  }
}

import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const { authorization } = req.headers;
    const { period } = req.query;
    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

    if (!backendUrl) {
      return res.status(500).json({ message: "Backend URL not configured" });
    }

    const headers: any = {
      "Content-Type": "application/json",
    };

    if (authorization) {
      headers.Authorization = authorization;
    }

    const queryParams = period ? `?period=${period}` : "";
    const response = await fetch(
      `${backendUrl}/api/track-links/performance-overview${queryParams}`,
      {
        method: "GET",
        headers,
        credentials: "include",
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Backend error:", errorText);
      return res.status(response.status).json({
        message: "Failed to fetch track links performance overview",
        error: errorText,
      });
    }

    const data = await response.json();
    return res.status(200).json(data);
  } catch (error: any) {
    console.error("Track links performance overview API error:", error);
    return res.status(500).json({
      message: "Internal server error",
      error: error.message,
    });
  }
}

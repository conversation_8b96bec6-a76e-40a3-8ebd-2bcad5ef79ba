import { AppError } from "@/interfaces";
import { StrapiClient } from "@/utils/request";
import { sendApiError } from "@/utils/api-error-handler";
import { createApiContext } from "@/utils/api-middleware";
import type { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<any | AppError>
) {
  const { method } = req;

  if (method !== "GET") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    // Use centralized context with required auth
    const { token, cookies } = createApiContext(req, {
      requireAuth: true,
      forwardCookies: true,
    });

    // Call StrapiClient with token and all cookies
    const userData = await StrapiClient.getUserMe(token!, cookies);

    return res.status(200).json(userData);
  } catch (error: any) {
    console.error("Error in /api/users/me:", error);
    sendApiError(res, error, "Error fetching user data");
  }
}

import { NextApiRequest, NextApiResponse } from 'next';
import { createApiContext } from '@/utils/api-middleware';
import { sendApiError } from '@/utils/api-error-handler';
import { StrapiClient } from '@/utils/request';

interface UserPrompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  tags?: string[];
  isFavorite: boolean;
  usageCount: number;
  lastUsedAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface UpdatePromptRequest {
  title?: string;
  content?: string;
  description?: string;
  tags?: string[];
  isFavorite?: boolean;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ data: UserPrompt; message?: string } | { message: string } | any>
) {
  try {
    const { token } = createApiContext(req, { requireAuth: true });
    const { id } = req.query;

    if (!id || typeof id !== 'string') {
      return res.status(400).json({ error: 'Invalid prompt ID' });
    }

    if (req.method === 'GET') {
      // Get single user prompt
      const response: any = await StrapiClient.client.get(`/api/user-prompts/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return res.status(200).json(response.data);
    }

    if (req.method === 'PUT') {
      // Update user prompt
      const updateData: UpdatePromptRequest = req.body;

      const response: any = await StrapiClient.client.put(`/api/user-prompts/${id}`, updateData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return res.status(200).json(response.data);
    }

    if (req.method === 'DELETE') {
      // Delete user prompt
      const response: any = await StrapiClient.client.delete(`/api/user-prompts/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return res.status(200).json(response.data);
    }

    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error: any) {
    console.error('Error in user prompt API route:', error);
    sendApiError(res, error, 'Error processing user prompt request');
  }
}

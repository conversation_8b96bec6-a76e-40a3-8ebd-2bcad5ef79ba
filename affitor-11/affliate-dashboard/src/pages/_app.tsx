import { Provider } from "react-redux";
import type { AppProps } from "next/app";
import { useRouter } from "next/router";
import { wrapper } from "@/store";
import { ThemeProvider } from "next-themes";
import "@/styles/globals.css";
import "@/styles/yoopta-notion.css";
import { Header } from "@/components";
import { ToastContextProvider } from "@/context/ToastContext";
import ToastObserver from "@/components/ToastObserver";
import ReferralTracker from "@/components/ReferralTracker";
import SplitScreenLayout from "@/components/SplitScreenLayout";
import AiChatPortal from "@/components/AiChat/AiChatPortal";

export default function App({ Component, ...rest }: AppProps) {
  const { store, props } = wrapper.useWrappedStore(rest);
  const router = useRouter();

  // Hide header on specific pages
  const hideHeaderPages = ["/ad", "/admin", '/editor'];
  const shouldHideHeader =
    hideHeaderPages.some((page) => router.pathname.includes(page)) ||
    (router.pathname === "/admin" &&
      typeof window !== "undefined" &&
      window.location.hash.startsWith("#payout"));

  return (
    <Provider store={store}>
      <ToastContextProvider>
        <ToastObserver />
        <ReferralTracker />
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="relative min-h-screen">
            {/* Fixed Header - positioned outside main content flow */}
            {!shouldHideHeader && (
              <div className="fixed top-0 left-0 right-0 z-[100]">
                <Header />
              </div>
            )}

            {/* Main Content - full height with proper top offset */}
            <div
              className="min-h-screen"
              style={{
                marginTop: shouldHideHeader ? '0' : '75px'
              }}
            >
              <SplitScreenLayout>
                <Component {...props} />
              </SplitScreenLayout>
            </div>

            {/* AI Chat Portal - positioned outside main layout */}
            <AiChatPortal />
          </div>
        </ThemeProvider>
      </ToastContextProvider>
    </Provider>
  );
}

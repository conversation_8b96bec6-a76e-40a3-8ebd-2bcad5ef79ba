import Head from "next/head";
import Image from "next/image";
import { useRouter } from "next/router";
import { useSelector, useDispatch } from "react-redux";
import { selectIsAuthenticated } from "@/features/auth/auth.slice";
import { subscriptionTierActions } from "@/features/rootActions";
import { useEffect, useState } from "react";
import Upgrade from "@/containers/Profile/Upgrade";
import { Button } from "@/components/ui/button";

// FAQ Section
const FAQS = [
  {
    q: "How does Affitor help me find high-converting affiliate products faster than doing it manually?",
    a: "Affitor helps you skip hours of manual research by giving instant access to pre-vetted affiliate projects with proven performance. You get real-time data on traffic, commissions, and EPU (Earning Potential per User), plus smart filters to find offers that match your niche. See what's working through top ad creatives, and launch faster with AI-generated scripts and content ideas — all in one place.",
  },
  {
    q: "How exactly does Affitor help me save time and increase conversions by showing me real-time EPU, traffic sources, and proven ad creatives?",
    a: "Affitor aggregates and analyzes data from multiple sources, so you can instantly see which products are trending, what creatives are performing, and how much you can expect to earn per user. This lets you make smarter, data-backed decisions and launch campaigns with confidence.",
  },
  {
    q: "Where does Affitor get its viral video and ad content from? Is it updated in real time?",
    a: "Affitor scrapes and curates viral video and ad content from global sources, updating regularly so you always have access to the latest trends and top-performing creatives.",
  },
  {
    q: "Do I need to be experienced in affiliate marketing to use Affitor effectively?",
    a: "No experience required! Affitor is designed to be user-friendly for beginners, but powerful enough for pros. The platform guides you through every step, from finding offers to launching campaigns.",
  },
];

// Custom CTA button style
const ctaButtonClass =
  "px-8 py-3 text-lg font-semibold bg-[#4f63e7] text-white rounded-full shadow-md transition-all duration-200 hover:bg-[#3b4cc4] hover:shadow-lg focus:outline-none border-0";
const ctaButtonSmallClass =
  "px-6 py-2 text-base font-semibold bg-[#4f63e7] text-white rounded-full shadow-md transition-all duration-200 hover:bg-[#3b4cc4] hover:shadow-lg focus:outline-none border-0";

export default function LandingPage() {
  const router = useRouter();
  const dispatch = useDispatch();
  const isAuthenticated = useSelector(selectIsAuthenticated);

  // Fetch pricing tiers on mount (for Upgrade component)
  useEffect(() => {
    dispatch(subscriptionTierActions.fetchSubscriptionTiers());
  }, [dispatch]);

  // Handler for all trial buttons
  const handleStartTrial = () => {
    if (!isAuthenticated) {
      router.replace(
        `/authentication?redirect=${encodeURIComponent(router.asPath)}`
      );
      return;
    }
    // Start free trial for Basic tier (handled by Upgrade logic)
    // Optionally, you could dispatch a subscribe action here if you want instant subscribe
    router.push("/pricing"); // Or trigger the subscribe logic directly
  };

  return (
    <>
      <Head>
        <title>
          Affitor - The all-in-one AI tool to boost your Affiliate success
        </title>
        <meta
          name="description"
          content="Winning product. Viral content. Ready script. Fast income."
        />
      </Head>
      <main className="bg-white dark:bg-slate-900 min-h-screen w-full">
        {/* Hero Section */}
        <section className="max-w-6xl mx-auto px-4 py-12 flex flex-col items-center">
          <h1 className="text-3xl md:text-5xl font-extrabold text-center mb-6 dark:text-white">
            The All-in-one AI Tool to Boost Your Affiliate Success
          </h1>
          <p className="text-center text-lg mb-8 dark:text-slate-300">
            Winning product. Viral content. Ready script. Fast income
          </p>
          <Button
            className={ctaButtonClass + " mb-10 mt-2"}
            onClick={handleStartTrial}
          >
            Start Free Trial
          </Button>
          <Image
            src="/landing-pages/image_1.png"
            alt="Dashboard Preview"
            width={800}
            height={400}
            className="rounded-lg dark:bg-slate-800"
          />
        </section>

        {/* Affiliate Program Discovery */}
        <section className="max-w-6xl mx-auto px-4 py-12 flex flex-col md:flex-row items-center gap-8">
          <div className="flex-1">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 dark:text-white">
              One Click to Hundreds of Affiliate Programs
            </h2>
            <p className="mb-4 dark:text-slate-300">
              Discover your next high-converting offer by browsing our curated
              directory of top affiliate programs. Use smart filters to narrow
              down by niche, commission type, or payout model — and find the
              perfect match for your audience.
            </p>
            <Button
              className={ctaButtonClass + " mb-4"}
              onClick={handleStartTrial}
            >
              Start Free Trial
            </Button>
          </div>
          <div className="flex-1">
            <Image
              src="/landing-pages/image_2.png"
              alt="Affiliate Program Icons"
              width={600}
              height={300}
              className="rounded-lg dark:bg-slate-800"
            />
          </div>
        </section>

        {/* Viral Videos Section */}
        <section className="max-w-6xl mx-auto px-4 py-12 flex flex-col md:flex-row items-center gap-8">
          <div className="flex-1">
            <Image
              src="/landing-pages/image_3.png"
              alt="Viral Videos Preview"
              width={600}
              height={300}
              className="rounded-lg dark:bg-slate-800"
            />
          </div>
          <div className="flex-1">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 dark:text-white">
              Millions of Viral Videos Unlocked
            </h2>
            <p className="mb-4 dark:text-slate-300">
              Get instant access to global viral content linked to affiliate
              products. Scraped, sorted, and ready to use — so you can see
              what's working and launch faster.
            </p>
            <Button
              className={ctaButtonClass + " mb-4"}
              onClick={handleStartTrial}
            >
              Start Free Trial
            </Button>
          </div>
        </section>

        {/* Earning Potential Section */}
        <section className="max-w-6xl mx-auto px-4 py-12 flex flex-col md:flex-row items-center gap-8">
          <div className="flex-1">
            <h2 className="text-2xl md:text-3xl font-bold mb-2 dark:text-white">
              Earning Potential per User
            </h2>
            <p className="mb-4 dark:text-slate-300">
              Affitor shows you the EPU (Earning Potential per User) for each
              product — a powerful metric that helps you predict profits before
              you promote. No more blind guessing. Just data-backed decisions.
            </p>
            <Button
              className={ctaButtonClass + " mb-4"}
              onClick={handleStartTrial}
            >
              Start Free Trial
            </Button>
          </div>
          <div className="flex-1">
            <Image
              src="/landing-pages/image_4.png"
              alt="EPU Cards"
              width={600}
              height={300}
              className="rounded-lg dark:bg-slate-800"
            />
          </div>
        </section>

        {/* Affiliate Tools Section */}
        <section className="max-w-6xl mx-auto px-4 py-12">
          <h2 className="text-2xl md:text-3xl font-bold mb-6 dark:text-white">
            Affiliate Tools
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                title: "Affiliate Project Hub",
                desc: "A curated library of pre-vetted affiliate projects with high earning potential. Skip the guesswork — pick from trusted, hot offers that actually convert.",
              },
              {
                title: "Real-Time Data",
                desc: "Live updates on traffic sources, commission rates, trends, and payout models. Always stay ahead with the freshest data in the affiliate game.",
              },
              {
                title: "Earning Potential per User",
                desc: "Instantly estimate how much you can earn from each product. Make smarter decisions before you promote — no more testing blind.",
              },
              {
                title: "Top Ads Video Library",
                desc: "Access a vault of top-performing ad creatives from across platforms. See what's working now, who's running it, and get inspired to replicate success.",
              },
              {
                title: "Social Listening Radar",
                desc: "Know what's buzzing. Track viral content, trending products, and audience reactions in real time — so you can ride the trend, not chase it.",
              },
              {
                title: "AI Script & Chat Assistant",
                desc: "Generate high-converting scripts, captions, and content angles with AI. Stuck for ideas? Let the AI chatbot brainstorm and build your next winning post.",
              },
            ].map((tool, i) => (
              <div
                key={i}
                className="rounded-2xl border bg-white dark:bg-slate-800 dark:border-slate-700 p-6 flex flex-col items-start shadow-sm hover:shadow-md transition-shadow duration-200 gap-2"
              >
                <Image
                  src={`/landing-pages/image_${i + 5}.png`}
                  alt={tool.title}
                  width={320}
                  height={160}
                  className="rounded-lg mb-4 object-cover w-full h-40 dark:bg-slate-700"
                />
                <h3 className="font-bold text-lg mb-1 text-left w-full dark:text-white">
                  {tool.title}
                </h3>
                <p className="text-left text-sm mb-1 w-full dark:text-slate-300">
                  {tool.desc}
                </p>
                <a
                  href="/"
                  className="text-blue-600 dark:text-blue-400 font-medium text-sm hover:underline mt-1 text-left w-full block"
                >
                  Explore Details &gt;
                </a>
              </div>
            ))}
          </div>
        </section>

        {/* Pricing Section */}
        <section className="max-w-6xl mx-auto px-4 py-12">
          <h2 className="text-2xl md:text-3xl font-bold mb-4">Pricing</h2>
          {/* Reuse the Upgrade component for pricing cards and logic */}
          <Upgrade />
        </section>

        {/* FAQ Section */}
        <section className="max-w-4xl mx-auto px-4 py-12">
          <h2 className="text-2xl md:text-3xl font-bold mb-6 dark:text-white">
            Frequently Asked Questions
          </h2>
          <div className="space-y-4">
            {FAQS.map((faq, i) => (
              <FAQItem key={i} question={faq.q} answer={faq.a} />
            ))}
          </div>
        </section>

        {/* Founder Section */}
        {/* <section className="max-w-2xl mx-auto px-4 py-16 flex flex-col items-center">
          <div className="relative w-full flex flex-col items-center justify-center">
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-100 via-white to-blue-50 dark:from-blue-900 dark:via-slate-900 dark:to-blue-950 opacity-80 -z-10" />
            <div className="w-full bg-white dark:bg-slate-900 rounded-2xl border-2 border-blue-200 dark:border-blue-700 shadow-xl dark:shadow-blue-900/30 p-8 flex flex-col items-start">
              <Image
                src="/landing-pages/image_11.png"
                alt="Founder Avatar"
                width={200}
                height={200}
                className="rounded-full mb-4 mx-auto dark:bg-slate-800"
              />
              <h3 className="text-2xl font-bold mt-2 mb-1 text-slate-900 dark:text-white text-center w-full">
                Son Piaz
              </h3>
              <div className="text-blue-600 dark:text-blue-400 font-medium mb-4 text-base text-center w-full">
                Founder & Affiliate Expert
              </div>
              <p className="mb-8 text-slate-700 dark:text-slate-300 text-lg leading-relaxed text-left w-full max-w-xl">
                After 10 years in affiliate marketing, I built Affitor to solve
                the exact problems I faced: wasting time hunting for offers,
                second-guessing what works, and struggling to scale. This tool
                is everything I wish I had when I started — powerful, practical,
                and built with one clear goal: to help affiliates get results
                faster. No fluff. Just what really matters.
              </p>
              <Button
                className={ctaButtonClass + " w-full mt-2"}
                onClick={handleStartTrial}
              >
                Try for FREE
              </Button>
            </div>
          </div>
        </section> */}
      </main>
    </>
  );
}

// FAQ Accordion Item
function FAQItem({ question, answer }: { question: string; answer: string }) {
  const [open, setOpen] = useState(false);
  return (
    <div
      className={
        "rounded-2xl bg-white dark:bg-slate-800 border shadow-sm dark:border-slate-700 transition-shadow duration-200 " +
        (open ? "shadow-md" : "")
      }
    >
      <button
        className="w-full flex items-center justify-between px-6 py-4 focus:outline-none"
        onClick={() => setOpen((v) => !v)}
        aria-expanded={open}
      >
        <span className="font-semibold text-lg text-left dark:text-white">
          {question}
        </span>
        <span
          className="ml-4 text-2xl text-blue-500 dark:text-blue-400 transition-transform duration-200"
          style={{ transform: open ? "rotate(45deg)" : "rotate(0deg)" }}
        >
          {open ? "−" : "+"}
        </span>
      </button>
      <div
        className={`overflow-hidden transition-all duration-300 px-6 ${
          open ? "max-h-40 py-2" : "max-h-0 py-0"
        }`}
        style={{
          maxHeight: open ? 200 : 0,
        }}
      >
        <div className="text-slate-700 dark:text-slate-300 text-base pb-4">
          {answer}
        </div>
      </div>
    </div>
  );
}

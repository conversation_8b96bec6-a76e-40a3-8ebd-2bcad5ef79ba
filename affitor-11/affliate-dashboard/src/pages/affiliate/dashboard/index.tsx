import React, { useEffect } from "react";
import { NextPage } from "next";
import { useRouter } from "next/router";
import Head from "next/head";

// This page is now deprecated since we're using hash-based navigation
const AffiliateDashboard: NextPage = () => {
  const router = useRouter();
  
  useEffect(() => {
    // Redirect to the hash-based dashboard
    router.replace("/affiliate#dashboard");
  }, [router]);

  return (
    <>
      <Head>
        <title>Affiliate Dashboard - Redirecting...</title>
      </Head>
      <div className="flex items-center justify-center min-h-screen">
        <p>Redirecting to new dashboard...</p>
      </div>
    </>
  );
};

export default AffiliateDashboard;

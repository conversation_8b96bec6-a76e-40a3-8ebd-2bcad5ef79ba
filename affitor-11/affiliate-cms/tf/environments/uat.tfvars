# ECR Image configuration
ecr_image = "779037175265.dkr.ecr.us-east-1.amazonaws.com/affiliate:uat"
bucket_name    = "affiliate-ecs-service-app-configuration-uat"

# AWS Region
aws_region = "us-east-1"

# VPC and Network settings
vpc_cidr = "10.1.0.0/16"
public_subnets = ["10.1.1.0/24", "10.1.2.0/24"]
private_subnets = ["********/24", "********/24"]

# ECS settings
ecs_cluster_name = "affiliate-uat-cluster"
ecs_service_name = "affiliate-uat-service"
container_port = 80
desired_count = 1
cpu = 256
memory = 512

# Load balancer settings
lb_name = "affiliate-uat-lb"
lb_internal = false
lb_listener_port = 80
lb_health_check_path = "/health"
engine_version = 14.9

# Tags
environment = "uat"

# Add this to your tfvars file
certificate_arn = "arn:aws:acm:us-east-1:779037175265:certificate/039c4b1b-1743-4b3e-bf4b-e6bf28d45516"
ip_address = "***********"

# Aurora Serverless v2 capacity settings - optimized for UAT to save costs
serverless_min_capacity = 0.5  # Minimum capacity (0.5 ACU)
serverless_max_capacity = 2.0  # Maximum capacity (2.0 ACU)
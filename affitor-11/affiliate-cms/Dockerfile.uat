# Build stage
FROM node:18-alpine3.18 AS build

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++ 

# Copy package files and install production dependencies
COPY package.json yarn.lock ./
RUN yarn install --production

# Copy the rest of the app and build
COPY . .
RUN yarn build

# Production stage
FROM node:18-alpine3.18 AS uat

# Install runtime dependencies required by Chromium and Puppeteer
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    fontconfig \
    mesa-gl \
    libx11 \
    libxcomposite \
    libxdamage \
    libxext \
    libxrandr \
    libxrender \
    libxtst \
    at-spi2-core \
    cairo \
    pango \
    dbus-libs

WORKDIR /app

# Copy built app from build stage
COPY --from=build /app ./

ENV NODE_ENV=production
ENV PATH=/app/node_modules/.bin:$PATH

# Configure Puppeteer to use the system Chromium
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Create cache directory and set proper permissions
RUN mkdir -p /home/<USER>/.cache && chown -R node:node /home/<USER>/.cache && chown -R node:node /app

# Switch to non-root user
USER node

EXPOSE 1337

CMD ["yarn", "start"]

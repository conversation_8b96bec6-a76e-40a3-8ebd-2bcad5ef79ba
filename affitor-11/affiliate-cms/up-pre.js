const testEmails = ['<EMAIL>'];
const emailsRun = [
  ...new Set([
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    'la<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ]),
];
const emailsNew = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

const emailsNew2 = ['<EMAIL>', '<EMAIL>'];
const emailsNew3 = ['<EMAIL>'];
const emailsNew4 = ['<EMAIL>', '<EMAIL>'];
const emailsNew5 = ['<EMAIL>', '<EMAIL>'];
const emailsNew6 = ['<EMAIL>'];
const emailsNew7 = ['<EMAIL>', '<EMAIL>'];
const emailsNew8 = ['<EMAIL>'];
const emailsNew9 = ['<EMAIL>'];
const emailsNew10 = ['<EMAIL>'];
const emailsNew11 = ['<EMAIL>'];

const emails = emailsNew11;

const proTier = { id: 46, documentId: 'rf0ugx9y796eslk2rv31dpq8' };
const preTier = { id: 14, documentId: 'dn08seeimcp1udasdm0v9wg4' };

const API_BASE_URL = 'http://prod-alb-521497661.us-east-1.elb.amazonaws.com';
const AUTH_TOKEN =
  'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwiaWF0IjoxNzQ5OTU2MTI4LCJleHAiOjE3NTI1NDgxMjh9.sCjxt5K2cH8rlpQBzj3wSCh2LvrIhIGMr8VzA-RSFf4';

async function searchUserByEmail(email) {
  const url = `${API_BASE_URL}/content-manager/collection-types/plugin::users-permissions.user?page=1&pageSize=10&sort=username%3AASC&_q=${encodeURIComponent(email)}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
      Connection: 'keep-alive',
      'User-Agent':
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      accept: 'application/json',
      authorization: AUTH_TOKEN,
      'content-type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to search user: ${response.statusText}`);
  }

  const search = await response.json();
  if (!search.results || search.results.length === 0) {
    return { email, error: 'User not found' };
  }

  return search.results[0];
}

async function getUserTrackingDetails(trackingDocumentId) {
  const url = `${API_BASE_URL}/content-manager/collection-types/api::user-tracking-request.user-tracking-request/${trackingDocumentId}?`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
      Connection: 'keep-alive',
      'User-Agent':
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      accept: 'application/json',
      authorization: AUTH_TOKEN,
      'content-type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to get tracking details: ${response.statusText}`);
  }

  return response.json();
}

async function updateUserTracking(trackingDocumentId, trackingData) {
  const url = `${API_BASE_URL}/content-manager/collection-types/api::user-tracking-request.user-tracking-request/${trackingDocumentId}/actions/publish?`;

  const updatePayload = {
    request_count: trackingData.request_count || 0,
    request_limit: 99999999,
    last_request_date: trackingData.last_request_date,
    current_period_end: '2026-06-15T17:00:00.000Z',
    statistics: trackingData.statistics || {},
    users_permissions_user: { connect: [], disconnect: [] },
    subscription_tier: {
      connect: [proTier],
      disconnect: [preTier],
    },
    transaction: { connect: [], disconnect: [] },
  };

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
      Connection: 'keep-alive',
      Origin: API_BASE_URL,
      'User-Agent':
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      accept: 'application/json',
      authorization: AUTH_TOKEN,
      'content-type': 'application/json',
    },
    body: JSON.stringify(updatePayload),
  });

  if (!response.ok) {
    throw new Error(`Failed to update tracking: ${response.statusText}`);
  }

  return response.json();
}

async function updateUserData(userDocumentId, userData) {
  const url = `${API_BASE_URL}/content-manager/collection-types/plugin::users-permissions.user/${userDocumentId}?`;

  const updatePayload = {
    username: userData.username,
    email: userData.email,
    confirmed: true,
    blocked: false,
    stripe_customer_id: userData.stripe_customer_id || null,
    role: { connect: [], disconnect: [] },
    user_tracking_request: { connect: [], disconnect: [] },
    subscription_tier: {
      connect: [proTier],
      disconnect: [preTier],
    },
    referrer: { connect: [], disconnect: [] },
    referral: { connect: [], disconnect: [] },
    address: userData.address || '1',
  };

  const response = await fetch(url, {
    method: 'PUT',
    headers: {
      'Accept-Language': 'en-US,en;q=0.9,vi;q=0.8',
      Connection: 'keep-alive',
      Origin: API_BASE_URL,
      'User-Agent':
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      accept: 'application/json',
      authorization: AUTH_TOKEN,
      'content-type': 'application/json',
    },
    body: JSON.stringify(updatePayload),
  });

  if (!response.ok) {
    throw new Error(`Failed to update user: ${response.statusText}`);
  }

  return response.json();
}

async function getUserTrackingByEmail(email, user) {
  try {
    const trackingDocumentId = user.user_tracking_request?.documentId;

    if (!trackingDocumentId) {
      return { email, user, error: 'No tracking request found' };
    }

    // Get detailed tracking information
    const trackingDetails = await getUserTrackingDetails(trackingDocumentId);

    // Update tracking with new values
    const updateResult = await updateUserTracking(trackingDocumentId, trackingDetails.data);

    // Update user data
    const userUpdateResult = await updateUserData(user.documentId, user);

    return {
      email,
      user,
      trackingDetails,
      updateResult,
      userUpdateResult,
      success: true,
    };
  } catch (error) {
    return { email, error: error.message };
  }
}

async function processAllEmails() {
  console.log('Processing emails...');

  const results = [];
  for (const email of emails) {
    console.log(`Processing: ${email}`);
    const user = await searchUserByEmail(email);
    const result = await getUserTrackingByEmail(email, user);
    results.push(result);

    if (result.error) {
      console.log(`Error for ${email}: ${result.error}`);
    } else {
      console.log(
        `Success for ${email}: Found user ${result.user.username}, updated tracking and user data`
      );
    }
  }

  return results;
}

// Run the script
processAllEmails()
  .then((results) => {
    console.log('\n=== RESULTS ===');

    const notFoundEmails = results
      .filter((result) => result.error === 'User not found')
      .map((result) => result.email);
    const successfulEmails = results
      .filter((result) => result.success)
      .map((result) => result.email);
    const otherErrors = results.filter(
      (result) => result.error && result.error !== 'User not found'
    );

    console.log('\n=== SUMMARY ===');
    console.log(`Total emails processed: ${results.length}`);
    console.log(`Successful updates: ${successfulEmails.length}`);
    console.log(`Emails not found: ${notFoundEmails.length}`);
    console.log(`Other errors: ${otherErrors.length}`);

    if (notFoundEmails.length > 0) {
      console.log('\n=== EMAILS NOT FOUND ===');
      notFoundEmails.forEach((email) => console.log(email));
    }

    if (otherErrors.length > 0) {
      console.log('\n=== OTHER ERRORS ===');
      otherErrors.forEach((result) => console.log(`${result.email}: ${result.error}`));
    }

    console.log('\n=== FULL RESULTS ===');
    results.forEach((result) => {
      if (result.success) {
        console.log(`✅ SUCCESS: ${result.email}`);
      } else {
        console.log(`❌ FAILED: ${result.email} - ${result.error}`);
      }
    });
  })
  .catch((error) => {
    console.error('Script failed:', error);
  });

/**
 * aiscript service
 *
 * This service handles AI script processing and special command execution.
 *
 * Architecture Changes:
 * - Migrated from chat-message service to aiscript system for AI interactions
 * - Special commands (like /generate) are now processed here before AI model calls
 * - Maintains the same user experience while using the new aiscript architecture
 *
 * Supported Commands:
 * - /generate [title] - Creates a new page with optional title
 * - /help or /commands - Shows available commands
 *
 * The service processes commands first, then falls back to AI model processing
 * for regular messages.
 */

import { factories } from '@strapi/strapi';
import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { HumanMessage, SystemMessage, AIMessage, BaseMessage } from '@langchain/core/messages';
import { HarmBlockThreshold, HarmCategory } from '@google/generative-ai';
import { N8nClient } from '../../../utils/request';
import { IAIScriptSession, IConversationMessage } from '../interfaces';
import { YooptaContentValue, YooptaBlockType } from '../../../../types/yoopta-editor';

export default factories.createCoreService('api::aiscript.aiscript', {
  async sendToChatGPT(
    message: string | BaseMessage[],
    modelVersion: string = 'gpt-3.5-turbo'
  ): Promise<string> {
    try {
      // Initialize the OpenAI chat model
      const model = new ChatOpenAI({
        openAIApiKey: process.env.OPENAI_API_KEY,
        modelName: modelVersion,
      });

      // Handle both string messages and message arrays
      const messages = Array.isArray(message)
        ? message
        : [new HumanMessage(message)];

      const response = await model.invoke(messages);

      console.log('Response from OpenAI:', response);
      // Return the model's response
      return response.content.toString();
    } catch (error) {
      console.error('Error sending message to OpenAI:', error);
      throw error;
    }
  },

  async sendToClaude(
    message: string | BaseMessage[],
    modelVersion: string = 'claude-3-opus-20240229'
  ): Promise<string> {
    try {
      // Initialize the Anthropic chat model
      const model = new ChatAnthropic({
        anthropicApiKey: process.env.ANTHROPIC_API_KEY,
        modelName: modelVersion,
      });

      // Handle both string messages and message arrays
      const messages = Array.isArray(message)
        ? message
        : [new HumanMessage(message)];

      const response = await model.invoke(messages);

      console.log('Response from Claude:', response);
      // Return the model's response
      return response.content.toString();
    } catch (error) {
      console.error('Error sending message to Claude:', error);
      throw error;
    }
  },

  async sendToGemini(
    message: string | BaseMessage[],
    modelVersion: string = 'gemini-1.5-pro-002'
  ): Promise<string> {
    try {
      // Initialize the Google Gemini model with safety settings
      const model = new ChatGoogleGenerativeAI({
        apiKey: process.env.GOOGLE_API_KEY,
        maxOutputTokens: 2048,
        model: modelVersion, // Use modelName instead of model
        safetySettings: [
          {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
            threshold: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
          },
          {
            category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
            threshold: HarmBlockThreshold.BLOCK_LOW_AND_ABOVE,
          },
        ],
      });

      // Handle both string messages and message arrays
      const messages = Array.isArray(message)
        ? message
        : [new HumanMessage(message)];

      const response = await model.invoke(messages);

      console.log('Response from Gemini:', response);
      // Return the model's response
      return response.content.toString();
    } catch (error) {
      console.error('Error sending message to Gemini:', error);
      throw error;
    }
  },

  async sendToN8N(
    message: string,
    sessionId: string,
    modelVersion: string = 'n8n-gpt'
  ): Promise<string> {
    try {
      let response;
      if (modelVersion === 'n8n-gpt') {
        response = await N8nClient.sendToChatGPT(message, sessionId);
      } else if (modelVersion === 'n8n-gemini') {
        response = await N8nClient.sendToGemini(message, sessionId);
      } else {
        throw new Error(`Unsupported n8n model version: ${modelVersion}`);
      }

      console.log('Response from N8N:', !!response);

      // The response should now be a string if successfully parsed by N8nClient
      if (typeof response === 'string') {
        return response;
      }

      // As a fallback, try to extract from different potential structures
      if (Array.isArray(response) && response.length > 0 && response[0].output) {
        return response[0].output;
      }

      return response.content || response.text || JSON.stringify(response);
    } catch (error) {
      console.error(`Error sending message to N8N ${modelVersion}:`, error);
      throw error;
    }
  },

  // Helper method to convert conversation history to LangChain messages
  convertToLangChainMessages(history: IConversationMessage[]): BaseMessage[] {
    return history.map(msg => {
      switch (msg.role) {
        case 'system':
          return new SystemMessage(msg.content);
        case 'user':
          return new HumanMessage(msg.content);
        case 'assistant':
          return new AIMessage(msg.content);
        default:
          return new HumanMessage(msg.content);
      }
    });
  },

  // Helper method to send two-step AI request for default prompts
  async sendTwoStepAIRequest(
    promptContent: string,
    userMessage: string,
    selectedModel: string,
    session: IAIScriptSession
  ): Promise<string> {
    const requestId = `${session.session_id}-${Date.now()}`;
    console.log(`[${requestId}] Starting two-step AI interaction for default prompt`);
    console.log(`[${requestId}] Model: ${selectedModel}`);
    console.log(`[${requestId}] Prompt length: ${promptContent.length} characters`);
    console.log(`[${requestId}] User message length: ${userMessage.length} characters`);

    try {
      // Step 1: Send the prompt to establish context
      console.log(`[${requestId}] Step 1: Establishing context with prompt`);
      const step1StartTime = Date.now();
      let contextResponse: string;

      if (selectedModel.startsWith('gpt')) {
        console.log(`[${requestId}] Step 1: Using ChatGPT provider`);
        contextResponse = await this.sendToChatGPT([new SystemMessage(promptContent)], selectedModel);
      } else if (selectedModel.startsWith('claude')) {
        console.log(`[${requestId}] Step 1: Using Claude provider`);
        contextResponse = await this.sendToClaude([new SystemMessage(promptContent)], selectedModel);
      } else if (selectedModel.startsWith('gemini')) {
        console.log(`[${requestId}] Step 1: Using Gemini provider`);
        contextResponse = await this.sendToGemini([new SystemMessage(promptContent)], selectedModel);
      } else if (selectedModel.startsWith('n8n')) {
        // N8N already handles session context, so we'll use the original single-step approach
        console.log(`[${requestId}] Using N8N provider - falling back to single-step approach`);
        return await this.sendToN8N(`${promptContent}\n\nUser message: ${userMessage}`, session.session_id, selectedModel);
      } else {
        // Default to GPT
        console.log(`[${requestId}] Step 1: Unknown model, defaulting to ChatGPT`);
        contextResponse = await this.sendToChatGPT([new SystemMessage(promptContent)], 'gpt-3.5-turbo');
      }

      const step1Duration = Date.now() - step1StartTime;
      console.log(`[${requestId}] Step 1 completed in ${step1Duration}ms`);
      console.log(`[${requestId}] Context response length: ${contextResponse.length} characters`);

      // Step 2: Send the user message with the established context
      console.log(`[${requestId}] Step 2: Sending user message with context`);
      const step2StartTime = Date.now();
      const conversationMessages = [
        new SystemMessage(promptContent),
        new AIMessage(contextResponse),
        new HumanMessage(userMessage)
      ];

      console.log(`[${requestId}] Step 2: Conversation has ${conversationMessages.length} messages`);

      let finalResponse: string;

      if (selectedModel.startsWith('gpt')) {
        console.log(`[${requestId}] Step 2: Using ChatGPT provider`);
        finalResponse = await this.sendToChatGPT(conversationMessages, selectedModel);
      } else if (selectedModel.startsWith('claude')) {
        console.log(`[${requestId}] Step 2: Using Claude provider`);
        finalResponse = await this.sendToClaude(conversationMessages, selectedModel);
      } else if (selectedModel.startsWith('gemini')) {
        console.log(`[${requestId}] Step 2: Using Gemini provider`);
        finalResponse = await this.sendToGemini(conversationMessages, selectedModel);
      } else {
        // Default to GPT
        console.log(`[${requestId}] Step 2: Unknown model, defaulting to ChatGPT`);
        finalResponse = await this.sendToChatGPT(conversationMessages, 'gpt-3.5-turbo');
      }

      const step2Duration = Date.now() - step2StartTime;
      const totalDuration = Date.now() - (step1StartTime - step1Duration + step1Duration);
      console.log(`[${requestId}] Step 2 completed in ${step2Duration}ms`);
      console.log(`[${requestId}] Final response length: ${finalResponse.length} characters`);
      console.log(`[${requestId}] Total two-step process completed in ${totalDuration}ms`);

      return finalResponse;

    } catch (error) {
      console.error(`[${requestId}] Error in two-step AI request:`, error);
      console.error(`[${requestId}] Error details:`, {
        message: error.message,
        stack: error.stack,
        model: selectedModel,
        sessionId: session.session_id
      });
      throw error;
    }
  },

  // Updated function to process user scripts with AI models
  async processUserScript({
    message,
    session,
    promptId,
    isUseDefaultPrompt = true,
  }: {
    message: string;
    session: IAIScriptSession;
    promptId?: string;
    isUseDefaultPrompt?: boolean;
  }): Promise<string> {
    const requestId = `${session.session_id}-${Date.now()}`;
    let scriptId = null;

    console.log(`[${requestId}] Starting processUserScript`);
    console.log(`[${requestId}] Session ID: ${session.session_id}`);
    console.log(`[${requestId}] Message length: ${message.length} characters`);
    console.log(`[${requestId}] Prompt ID: ${promptId || 'none'}`);
    console.log(`[${requestId}] Use default prompt: ${isUseDefaultPrompt}`);

    try {
      // Check for special commands before processing with AI
      if (message.trim().startsWith('/generate')) {
        console.log(`[${requestId}] Processing /generate command`);
        return await this.handleGenerateCommand(message, session);
      }

      if (message.trim() === '/help' || message.trim() === '/commands') {
        console.log(`[${requestId}] Processing help command`);
        return await this.handleHelpCommand();
      }
      // Create an initial record to track this execution
      if (session) {
        try {
          console.log(`[${requestId}] Creating initial AIScript record`);
          const scriptTitle = `Script execution - ${new Date().toISOString()}`;
          const initialRecord = await strapi.entityService.create('api::aiscript.aiscript', {
            data: {
              title: scriptTitle,
              status: 'processing',
              aiscript_session: session.id,
              input: message,
              publishedAt: new Date(),
            },
          });

          scriptId = initialRecord.id;
          console.log(`[${requestId}] Created initial AIScript record with ID: ${scriptId}`);
        } catch (err) {
          console.error(`[${requestId}] Error creating initial aiscript record:`, err);
          // Continue even if record creation fails
        }
      }

      // Fetch the global settings to get AI model preference
      console.log(`[${requestId}] Fetching global settings for AI model preference`);
      const globalSettings = await strapi.entityService.findMany('api::global.global');

      // Use the aiModel from global settings if available,
      // otherwise fall back to environment variable or default
      const selectedModel =
        globalSettings?.aiModel || process.env.DEFAULT_AI_MODEL || 'gpt-3.5-turbo';

      console.log(`[${requestId}] Selected AI model: ${selectedModel}`);

      // Get the prompt if promptId is provided
      let finalMessage = message;
      let promptContent = null;
      let useDefaultPromptTwoStep = false;

      if (promptId) {
        // Get specific prompt if promptId is provided
        const prompt = await strapi.service('api::prompt.prompt').getPromptById(promptId);
        if (prompt) {
          promptContent = prompt.content;
          // Use single-step for specific prompts
          finalMessage = `${promptContent}\n\nUser message: ${message}`;
          console.log('Applied specific prompt to message');
        }
      } else if (isUseDefaultPrompt) {
        // Get default prompt and use two-step process
        const defaultPrompt = await strapi.service('api::prompt.prompt').getDefaultPrompt();
        if (defaultPrompt) {
          promptContent = defaultPrompt.content;
          useDefaultPromptTwoStep = true;
          console.log('Will use two-step process for default prompt');
        }
      }

      // Determine provider and use the exact model version
      let response: string;

      if (useDefaultPromptTwoStep && promptContent) {
        // Use two-step process for default prompts
        response = await this.sendTwoStepAIRequest(promptContent, message, selectedModel, session);
      } else {
        // Use single-step process for specific prompts or no prompt
        if (selectedModel.startsWith('gpt')) {
          response = await this.sendToChatGPT(finalMessage, selectedModel);
        } else if (selectedModel.startsWith('claude')) {
          response = await this.sendToClaude(finalMessage, selectedModel);
        } else if (selectedModel.startsWith('gemini')) {
          response = await this.sendToGemini(finalMessage, selectedModel);
        } else if (selectedModel.startsWith('n8n')) {
          response = await this.sendToN8N(finalMessage, session.session_id, selectedModel);
        } else {
          // Default to GPT if model not recognized
          console.warn('Unrecognized model:', selectedModel, 'defaulting to gpt-3.5-turbo');
          response = await this.sendToChatGPT(finalMessage, 'gpt-3.5-turbo');
        }
      }

      // Update the aiscript record with the response
      if (session && scriptId) {
        try {
          await strapi.entityService.update('api::aiscript.aiscript', scriptId, {
            data: {
              model_version: selectedModel,
              output: response,
              content: finalMessage,
              status: 'completed',
              completed_at: new Date(),
            },
          });
          console.log('Updated AIScript record with response');
        } catch (err) {
          console.error('Error updating aiscript record:', err);
          // Continue even if record update fails
        }
      }

      return response;
    } catch (error) {
      console.error('Error processing user script with AI:', error);

      // Update the record with the error if we created one
      if (scriptId) {
        try {
          await strapi.entityService.update('api::aiscript.aiscript', scriptId, {
            data: {
              output: error.message,
              status: 'error',
              completed_at: new Date(),
            },
          });
        } catch (updateErr) {
          console.error('Error updating aiscript record with error status:', updateErr);
        }
      }

      return 'Sorry, there was an error processing your script with the AI service.';
    }
  },

  // Command handlers for special AI script commands
  async handleGenerateCommand(message: string, session: IAIScriptSession): Promise<string> {
    try {
      // Get the user from the session
      const user = session.users_permissions_user;
      if (!user) {
        return 'Sorry, I could not identify the user for this session. Please try again.';
      }

      // Parse the command to extract page title or use default
      const commandParts = message.trim().split(' ');
      let pageTitle = 'Untitled Page';

      if (commandParts.length > 1) {
        // Extract title from command like "/generate My New Page"
        pageTitle = commandParts.slice(1).join(' ');
      }

      // Create initial page content with Yoopta Editor structure
      const initialContent: YooptaContentValue = {
        'block-1': {
          id: 'block-1',
          type: 'paragraph' as YooptaBlockType,
          value: [{ text: 'Start writing your content here...' }],
          meta: { order: 0 }
        }
      };

      // Create the page using the page service
      const pageService = strapi.service('api::page.page');
      const newPage = await pageService.createUserPage(user.id, {
        title: pageTitle,
        content: initialContent,
        excerpt: '',
        status: 'draft',
      });

      if (newPage) {
        return `<div class="space-y-3">
          <div class="flex items-center gap-2">
            <span class="text-green-500">✅</span>
            <strong>Page "${pageTitle}" created successfully!</strong>
          </div>

          <div class="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <div class="font-medium text-blue-800 mb-2">🎯 Ready to edit your page?</div>
            <a href="/editor/${newPage.documentId}"
               class="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
               onclick="window.open('/editor/${newPage.documentId}', '_blank'); return false;">
              Open Page Editor
            </a>
          </div>

          <div class="text-sm text-gray-600">
            <div class="font-medium mb-1">What's next:</div>
            <ul class="list-disc list-inside space-y-1">
              <li>Add rich content using our editor</li>
              <li>Customize the layout and styling</li>
              <li>Publish when you're ready to share</li>
            </ul>
          </div>

          <div class="text-xs text-gray-500 bg-gray-50 p-2 rounded">
            💡 <strong>Tip:</strong> You can also access your pages from your Profile section.
          </div>
        </div>`;
      } else {
        return 'Sorry, there was an error creating your page. Please try again.';
      }
    } catch (error) {
      console.error('Error handling generate command in aiscript:', error);
      return 'Sorry, there was an error creating your page. Please try again later.';
    }
  },

  async handleHelpCommand(): Promise<string> {
    return `<div class="space-y-4">
      <div class="border-b border-gray-200 pb-2">
        <h3 class="font-bold text-lg text-gray-800">🤖 Available Commands</h3>
        <p class="text-sm text-gray-600">Here are the special commands you can use:</p>
      </div>

      <div class="space-y-3">
        <div class="bg-blue-50 p-3 rounded-lg border border-blue-200">
          <div class="font-medium text-blue-800 mb-1">📄 /generate [title]</div>
          <div class="text-sm text-gray-700 mb-2">Create a new page with optional title</div>
          <div class="text-xs text-gray-600">
            <strong>Examples:</strong><br>
            • <code>/generate</code> - Creates "Untitled Page"<br>
            • <code>/generate My Blog Post</code> - Creates "My Blog Post"
          </div>
        </div>

        <div class="bg-gray-50 p-3 rounded-lg border border-gray-200">
          <div class="font-medium text-gray-800 mb-1">❓ /help or /commands</div>
          <div class="text-sm text-gray-700">Show this help message</div>
        </div>
      </div>

      <div class="text-xs text-gray-500 bg-yellow-50 p-2 rounded border border-yellow-200">
        💡 <strong>Tip:</strong> You can also ask me questions about affiliate marketing, content creation, or anything else!
      </div>
    </div>`;
  },
});

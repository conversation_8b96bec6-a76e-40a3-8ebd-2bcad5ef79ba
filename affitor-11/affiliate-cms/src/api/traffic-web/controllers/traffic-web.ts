/**
 * traffic-web controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::traffic-web.traffic-web', ({ strapi }) => ({
  async find(ctx) {
    const filters: any = ctx.query.filters;
    try {
      const affiliate = filters?.affiliate || {};
      await strapi.service('api::traffic-web.traffic-web').getTrafficData(affiliate?.documentId);
      // Call the default core action
      const { data, meta } = await super.find(ctx);
      return { data, meta };
    } catch (err) {
      console.error('LOG-find traffic web', err);
      return err;
    }
  },
}));

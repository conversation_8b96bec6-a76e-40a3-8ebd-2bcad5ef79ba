export interface IAffiliate {
  id?: number;
  name: string;
  company_name?: string;
  tag_line?: string;
  url?: string;
  domain?: string;
  slug?: string;
  monthly_traffic?: number;
  traffic_rank?: number;
  cookies_duration?: string;
  minimum_payout?: number;
  launch_year?: number;
  features?: string;
  country?: string;
  contact_information?: string;
  brand_keywords_youtube?: string;
  brand_keywords_tiktok?: string;
  currency?: 'USD' | 'EUR';
  avg_conversion?: number;
  avg_price?: number;
  pricing?: string;
  recurring?: string;
  recurring_priority?: number;

  // Relations
  categories?: any[];
  tags?: any[];
  payment_methods?: any[];
  commission?: any;
  traffic_webs?: any[];
  social_logs?: any[];
  industry?: any;

  // Media
  image?: any;

  // Rich content
  detail?: any;
  commission_detail?: any;

  // Component
  pricing_range?: any;

  // Timestamps and publishing fields
  createdAt?: Date;
  updatedAt?: Date;
  publishedAt?: Date;

  // airtable_data
  airtable_data?: {
    airtable_id: string;
    user_ref_link: string;
    user_ref_rate: number;
  };

  from_airtable?: boolean;
}

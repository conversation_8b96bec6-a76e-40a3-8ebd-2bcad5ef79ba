{"kind": "collectionType", "collectionName": "social_logs", "info": {"singularName": "social-log", "pluralName": "social-logs", "displayName": "Social Log", "description": ""}, "options": {"draftAndPublish": false}, "attributes": {"keyword": {"type": "string"}, "fetched_at": {"type": "datetime"}, "is_empty_result": {"type": "boolean"}, "platform": {"type": "string"}, "affiliate": {"type": "relation", "relation": "manyToOne", "target": "api::affiliate.affiliate", "inversedBy": "social_logs"}}}
export default {
  routes: [
    {
      method: 'POST',
      path: '/pages/auto-save/:id',
      handler: 'page.autoSave',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/pages/publish/:id',
      handler: 'page.publish',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/pages/duplicate/:id',
      handler: 'page.duplicate',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/pages/by-slug/:slug',
      handler: 'page.findBySlug',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/pages/my-pages',
      handler: 'page.findMyPages',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/pages/available-for-referrer-link',
      handler: 'page.findAvailableForReferrerLink',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/pages/track-view/:id',
      handler: 'page.trackView',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/pages/upload-image',
      handler: 'page.uploadImage',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};

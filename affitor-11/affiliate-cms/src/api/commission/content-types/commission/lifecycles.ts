import { errors } from '@strapi/utils';
const { ApplicationError } = errors;

async function handleEvent(event) {
  const {
    params: {
      data: { value_from, value_to, ...restData },
    },
  } = event;

  try {
    // Validate that both value_from and value_to exist
    if (value_from === undefined || value_to === undefined) {
      throw new ApplicationError('Both minimum and maximum commission values are required');
    }

    // Parse values to numbers
    const minCommission = parseFloat(value_from);
    const maxCommission = parseFloat(value_to);

    // Validate the parsed values
    if (isNaN(minCommission) || isNaN(maxCommission)) {
      throw new ApplicationError('Commission values must be valid numbers');
    }

    // Check that min is less than max
    if (minCommission > maxCommission) {
      throw new ApplicationError('Minimum commission value cannot be greater than maximum value');
    }

    // Calculate average commission
    const avgCommission = (minCommission + maxCommission) / 2;

    console.log(
      `Calculated avg_commission: ${avgCommission} from min: ${minCommission} and max: ${maxCommission}`
    );

    // Update data with max_percentage and avg_commission
    event.params.data = {
      ...restData,
      value_from,
      value_to,
      max_percentage: value_to,
      avg_commission: avgCommission,
    };
  } catch (error) {
    console.error('Error in commission validation:', error);
    // If it's already an ApplicationError, re-throw it
    if (error instanceof ApplicationError) {
      throw error;
    }
    // Otherwise, wrap it in an ApplicationError
    throw new ApplicationError(error.message || 'Error processing commission data');
  }
}

export default {
  beforeCreate: handleEvent,
  beforeUpdate: handleEvent,
};

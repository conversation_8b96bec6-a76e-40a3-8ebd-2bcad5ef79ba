/**
 * referrer service
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::referrer.referrer', ({ strapi }) => ({
  async register({ user }) {
    // Check if user exists
    if (!user || !user.id) {
      throw new Error('User is required to become a referrer');
    }

    // Check if user already has a referrer account
    const existingReferrer = await strapi.entityService.findMany('api::referrer.referrer', {
      filters: { user: user.id },
    });

    if (existingReferrer && existingReferrer.length > 0) {
      throw new Error('User is already registered as a referrer');
    }

    // Generate referral code based on username
    const referralCode = this.generateReferralCode(user.username || user.email);

    // Create new referrer linked to the user
    const referrer = await strapi.entityService.create('api::referrer.referrer', {
      data: {
        referral_code: referralCode,
        user: user.id,
        referrer_status: 'active',
        publishedAt: new Date(),
      },
    });

    return {
      success: true,
      data: referrer,
    };
  },

  // Helper method to generate a unique referral code
  generateReferralCode(identifier) {
    const prefix = identifier.substring(0, 3).toUpperCase();
    const random = Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, '0');
    return `${prefix}-${random}`;
  },

  /**
   * Calculate and update financial metrics for a single referrer
   * @param {string|number} referrerId - The ID of the referrer to recalculate
   * @param {Object} options - Optional parameters for filtering calculations
   * @param {string} options.dateFrom - Start date for filtering commissions (ISO string)
   * @param {string} options.dateTo - End date for filtering commissions (ISO string)
   * @returns {Object} Updated totals and calculation details
   */
  async recalculateReferrerMetrics(referrerId, options: any = {}) {
    try {
      // Validate referrer exists
      const referrer = await strapi.entityService.findOne('api::referrer.referrer', referrerId);
      if (!referrer) {
        throw new Error(`Referrer with ID ${referrerId} not found`);
      }

      // Build filters for commission query
      const filters: {
        referrer: any;
        createdAt?: {
          $gte?: Date;
          $lte?: Date;
        };
      } = {
        referrer: referrerId,
      };

      // Add date range filters if provided
      if (options.dateFrom || options.dateTo) {
        filters.createdAt = {};
        if (options.dateFrom) {
          filters.createdAt.$gte = new Date(options.dateFrom);
        }
        if (options.dateTo) {
          filters.createdAt.$lte = new Date(options.dateTo);
        }
      }

      // Get all commissions for this referrer
      const commissions = await strapi.entityService.findMany(
        'api::referral-commission.referral-commission',
        {
          filters,
          fields: ['gross_sale_amount', 'commission_amount', 'createdAt'],
          pagination: { limit: -1 }, // Get all commissions
        }
      );

      // Calculate totals
      let totalRevenue = 0;
      let totalEarnings = 0;
      let commissionCount = 0;

      commissions.forEach((commission) => {
        // Calculate total revenue from gross sale amounts
        const grossAmount = commission.gross_sale_amount;
        if (typeof grossAmount === 'number') {
          totalRevenue += grossAmount;
        } else if (typeof grossAmount === 'string') {
          totalRevenue += parseFloat(grossAmount) || 0;
        }

        // Calculate total earnings from commission amounts
        const commissionAmount = commission.commission_amount;
        if (typeof commissionAmount === 'number') {
          totalEarnings += commissionAmount;
        } else if (typeof commissionAmount === 'string') {
          totalEarnings += parseFloat(commissionAmount) || 0;
        }

        commissionCount++;
      });

      // Round to 2 decimal places for currency
      totalRevenue = Math.round(totalRevenue * 100) / 100;
      totalEarnings = Math.round(totalEarnings * 100) / 100;

      // Update referrer record with calculated values
      const updatedReferrer = await strapi.entityService.update(
        'api::referrer.referrer',
        referrerId,
        {
          data: {
            total_revenue: totalRevenue,
            total_earnings: totalEarnings,
          },
        }
      );

      return {
        success: true,
        referrerId,
        previousTotals: {
          total_revenue: referrer.total_revenue || 0,
          total_earnings: referrer.total_earnings || 0,
        },
        updatedTotals: {
          total_revenue: totalRevenue,
          total_earnings: totalEarnings,
        },
        calculationDetails: {
          commissionsProcessed: commissionCount,
          dateRange: {
            from: options.dateFrom || null,
            to: options.dateTo || null,
          },
        },
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      strapi.log.error(`Error recalculating metrics for referrer ${referrerId}:`, error);
      throw error;
    }
  },

  /**
   * Bulk recalculate financial metrics for all referrers or a filtered set
   * @param {Object} options - Optional parameters for filtering
   * @param {string} options.dateFrom - Start date for filtering commissions
   * @param {string} options.dateTo - End date for filtering commissions
   * @param {Array<string|number>} options.referrerIds - Specific referrer IDs to process
   * @returns {Object} Bulk operation results
   */
  async bulkRecalculateMetrics(options: {
    dateFrom?: string;
    dateTo?: string;
    referrerIds?: (string | number)[];
  } = {}) {
    try {
      // Build filters for referrer query
      const referrerFilters: { id?: { $in: (string | number)[] } } = {};
      if (options.referrerIds && options.referrerIds.length > 0) {
        referrerFilters.id = { $in: options.referrerIds };
      }

      // Get all referrers to process
      const referrers = await strapi.entityService.findMany('api::referrer.referrer', {
        filters: referrerFilters,
        fields: ['id', 'total_revenue', 'total_earnings'],
        pagination: { limit: -1 }, // Get all referrers
      });

      if (!referrers || referrers.length === 0) {
        return {
          success: true,
          message: 'No referrers found to process',
          processed: 0,
          errors: [],
          summary: {
            totalReferrers: 0,
            successfulUpdates: 0,
            failedUpdates: 0,
          },
          timestamp: new Date().toISOString(),
        };
      }

      const results = {
        processed: 0,
        errors: [],
        details: [],
        summary: {
          totalReferrers: referrers.length,
          successfulUpdates: 0,
          failedUpdates: 0,
          totalRevenueUpdated: 0,
          totalEarningsUpdated: 0,
        },
      };

      // Process each referrer
      for (const referrer of referrers) {
        try {
          const result = await this.recalculateReferrerMetrics(referrer.id, {
            dateFrom: options.dateFrom,
            dateTo: options.dateTo,
          });

          results.details.push(result);
          results.summary.successfulUpdates++;
          results.summary.totalRevenueUpdated += result.updatedTotals.total_revenue;
          results.summary.totalEarningsUpdated += result.updatedTotals.total_earnings;
          results.processed++;

          strapi.log.info(`Successfully recalculated metrics for referrer ${referrer.id}`);
        } catch (error) {
          strapi.log.error(`Error processing referrer ${referrer.id}:`, error);
          results.errors.push({
            referrerId: referrer.id,
            error: error.message,
            timestamp: new Date().toISOString(),
          });
          results.summary.failedUpdates++;
        }
      }

      // Round summary totals
      results.summary.totalRevenueUpdated = Math.round(results.summary.totalRevenueUpdated * 100) / 100;
      results.summary.totalEarningsUpdated = Math.round(results.summary.totalEarningsUpdated * 100) / 100;

      const successMessage = `Bulk recalculation completed. Successfully processed ${results.summary.successfulUpdates}/${results.summary.totalReferrers} referrers.`;
      strapi.log.info(successMessage);

      return {
        success: true,
        message: successMessage,
        processed: results.processed,
        errors: results.errors,
        details: results.details,
        summary: results.summary,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      strapi.log.error('Error in bulk recalculation:', error);
      throw error;
    }
  },
}));

import { errors } from '@strapi/utils';
const { UnauthorizedError, RateLimitError } = errors;

/**
 * Default configuration for tracked paths
 */
const defaultConfig = {
  // Each rule contains a method, path pattern, exclude patterns, and a threshold
  trackingRules: [
    // {
    //   method: 'GET',
    //   pathPattern: '/api/affiliates',
    //   excludePatterns: ['/affiliates/'],
    //   paginationThreshold: 1, // Pages beyond this number require authentication
    //   trackMode: 'pagination', // Track based on pagination threshold
    // },
    {
      method: 'GET',
      pathPattern: '/api/social-listenings',
      excludePatterns: ['/social-listenings/'],
      paginationThreshold: 1, // Pages beyond this number require authentication
      trackMode: 'pagination', // Track based on pagination threshold
    },
    {
      method: 'GET',
      pathPattern: '/api/affiliates/:id/summary',
      excludePatterns: [],
      trackMode: 'path', // Track all requests matching this path
    },
    {
      method: 'GET',
      pathPattern: '/api/social-listenings/transcript/:id',
      excludePatterns: [],
      trackMode: 'path', // Track all requests matching this path
    },
    // You can add more rules with different methods and paths
  ],
};

/**
 * Extract token from request
 */
const extractToken = (ctx) => {
  try {
    // From authorization header
    const authorization = ctx.request.header.authorization;
    if (authorization && authorization.startsWith('Bearer ')) {
      return authorization.substring(7);
    }

    // From query parameter
    if (ctx.query && ctx.query.token) {
      return ctx.query.token;
    }

    // From cookie
    if (ctx.cookies && ctx.cookies.get('token')) {
      return ctx.cookies.get('token');
    }

    return null;
  } catch (err) {
    console.error('Token extraction error:', err.message);
    return null;
  }
};

/**
 * Authenticate user from token
 */
const authenticateUser = async (token, strapi) => {
  if (!token) return null;

  try {
    // Verify token using Strapi's JWT service
    const jwtService = strapi.plugins['users-permissions'].services.jwt;
    const payload = await jwtService.verify(token);

    // Get user from database
    if (payload && payload.id) {
      return await strapi.entityService.findOne('plugin::users-permissions.user', payload.id, {});
    }

    return null;
  } catch (err) {
    console.error('Authentication error:', err.message);
    return null;
  }
};

/**
 * Checks if a user has exceeded their rate limits (both monthly and daily for basic tier)
 */
const checkUserLimits = async (ctx, strapi) => {
  // Check if user is authenticated
  const user = ctx.state.user;
  if (!user) {
    console.log('No user in context state for limit check');
    throw new UnauthorizedError('User not authenticated');
  }

  try {
    // Get the user tracking service
    const userTrackingService = strapi.service('api::user-tracking-request.user-tracking-request');

    // Get user stats to check usage limits
    const userStats = await userTrackingService.getUserStats(user.id);

    // Check monthly limit first (applies to all users)
    if (userStats.request_count >= userStats.request_limit) {
      throw new RateLimitError(
        `Monthly request limit reached (${userStats.request_count}/${userStats.request_limit}). Please try again next month or upgrade your plan.`
      );
    }

    // Check daily limit for basic tier users
    if (userStats.isBasicTier && userStats.daily) {
      const { daily_request_count, daily_request_limit, daily_remaining } = userStats.daily;

      if (daily_request_limit > 0 && daily_request_count >= daily_request_limit) {
        throw new RateLimitError(
          `Daily request limit reached (${daily_request_count}/${daily_request_limit}). Please try again tomorrow (resets at 00:00 UTC) or upgrade your plan.`
        );
      }

      // Add daily rate limit headers for basic tier users
      ctx.response.set('X-Daily-Rate-Limit-Limit', daily_request_limit.toString());
      ctx.response.set('X-Daily-Rate-Limit-Remaining', daily_remaining.toString());

      console.log(
        `Basic tier user ${user.id} - Daily: ${daily_request_count}/${daily_request_limit}, Monthly: ${userStats.request_count}/${userStats.request_limit}`
      );
    }

    // Add monthly rate limit headers to the response
    ctx.response.set('X-Rate-Limit-Limit', userStats.request_limit.toString());
    ctx.response.set(
      'X-Rate-Limit-Remaining',
      (userStats.request_limit - userStats.request_count - 1).toString()
    );

    console.log(
      `User authenticated: ${user.id}, Monthly requests: ${userStats.request_count}/${userStats.request_limit}${
        userStats.isBasicTier ? `, Daily: ${userStats.daily?.daily_request_count || 0}/${userStats.daily?.daily_request_limit || 0}` : ''
      }`
    );

    return true;
  } catch (error) {
    // If it's already a known error type, rethrow it
    if (error.name === 'UnauthorizedError' || error.name === 'RateLimitError') {
      throw error;
    }
    // For other errors, log but allow to continue
    console.error('Error checking user request limits:', error);
    return false;
  }
};

/**
 * Tracks a user request by incrementing their request count (both monthly and daily for basic tier)
 */
const trackUserRequest = async (ctx, strapi) => {
  try {
    // Get the current request path for accurate tracking
    const requestPath = ctx.request.url;

    // Normalize the path to keep it consistent
    // Remove query parameters to focus on the route itself
    const normalizedPath = requestPath.split('?')[0];

    // Increment the user's request count with the path information
    const userTrackingService = strapi.service('api::user-tracking-request.user-tracking-request');
    const trackingResult = await userTrackingService.trackUserRequest(
      ctx.state.user.id,
      normalizedPath
    );

    // Log tracking results with daily info if applicable
    if (trackingResult.dailyRemaining !== null && trackingResult.dailyRemaining !== undefined) {
      console.log(
        `Request counted for basic tier user ${ctx.state.user.id} - Path: ${normalizedPath}. Monthly: ${trackingResult.remaining}/${trackingResult.limit}, Daily: ${trackingResult.dailyRemaining}/${trackingResult.dailyLimit || 'N/A'}`
      );
    } else {
      console.log(
        `Request counted for user ${ctx.state.user.id} - Path: ${normalizedPath}. Remaining: ${trackingResult.remaining}/${trackingResult.limit}`
      );
    }
  } catch (error) {
    console.error('Error tracking user request:', error);
  }
};

/**
 * Converts a path pattern to a regex for proper matching
 * For example: '/api/affiliates/:id/summary' becomes /^\/api\/affiliates\/[^\/]+\/summary/
 */
const pathPatternToRegex = (pattern) => {
  // Replace path parameters (e.g., :id) with regex pattern for any character except slash
  const regexPattern = pattern.replace(/:[^/]+/g, '[^/]+').replace(/\//g, '\\/');

  return new RegExp(`^${regexPattern}`);
};

/**
 * Checks if the current request should be tracked based on configuration
 */
const shouldTrackRequest = (ctx, config) => {
  const { method, url } = ctx;

  // Check if the request matches any tracking rule
  return config.trackingRules.some((rule) => {
    // Check if method matches
    if (rule.method !== method) {
      return false;
    }

    // Convert pattern to regex if it's not already
    const pathRegex = rule.pathRegex || pathPatternToRegex(rule.pathPattern);
    if (!rule.pathRegex) {
      rule.pathRegex = pathRegex; // Cache the regex
    }

    // Check if URL matches the path pattern
    const matchesPath = pathRegex.test(url);
    if (!matchesPath) {
      return false;
    }

    // Check if URL is excluded by any exclude pattern
    const isExcluded =
      rule.excludePatterns && rule.excludePatterns.some((pattern) => url.includes(pattern));

    return matchesPath && !isExcluded;
  });
};

/**
 * Gets the applicable tracking rule for the current request
 */
const getTrackingRule = (ctx, config) => {
  const { method, url } = ctx;

  return config.trackingRules.find((rule) => {
    if (rule.method !== method) return false;

    // Convert pattern to regex if it's not already
    const pathRegex = rule.pathRegex || pathPatternToRegex(rule.pathPattern);
    if (!rule.pathRegex) {
      rule.pathRegex = pathRegex; // Cache the regex
    }

    // Check if URL matches the path pattern
    const matchesPath = pathRegex.test(url);
    if (!matchesPath) return false;

    const isExcluded =
      rule.excludePatterns && rule.excludePatterns.some((pattern) => url.includes(pattern));

    return matchesPath && !isExcluded;
  });
};

/**
 * Middleware to validate user authentication and track request usage for paginated resources
 */
export default (config = {}, { strapi }) => {
  // Merge provided config with defaults
  const mergedConfig = {
    ...defaultConfig,
    ...config,
    trackingRules: [...(defaultConfig.trackingRules || [])],
  };

  return async (ctx, next) => {
    console.log('User Tracking Middleware');

    // Debugging - Check if there's already a user in the state
    if (ctx.state.user) {
      console.log('User already in state:', ctx.state.user.id);
    } else {
      // let add logic to check if the path from the admin panel

      // Check if the request is from the admin panel
      const isAdminRequest =
        ctx.request.url.includes('/admin') || ctx.request.url.includes('/content-manager');
      if (isAdminRequest) {
        console.log('Request is from the admin panel, skipping authentication');
        await next();
        return;
      }

      // Check for token and authenticate user
      const token = extractToken(ctx);
      if (token) {
        console.log('Found token, attempting authentication');
        const user = await authenticateUser(token, strapi);
        if (user) {
          console.log('Authentication successful, user ID:', user.id);
          ctx.state.user = user;
        } else {
          // console.log('Authentication failed with token');
        }
      } else {
        // console.log('No token found in request');
      }
    }

    // Check if this request should be tracked
    if (shouldTrackRequest(ctx, mergedConfig)) {
      const rule = getTrackingRule(ctx, mergedConfig);
      console.log(`Request matches tracking rule: ${rule.method} ${rule.pathPattern}`);

      // Default to pagination mode if not specified
      const trackMode = rule.trackMode || 'pagination';

      if (trackMode === 'pagination') {
        // Handle pagination-based tracking (existing logic)
        const { pagination } = ctx.query || {};
        const page = pagination?.page;

        // Only check authentication for pages beyond the threshold
        if (page && Number(page) > rule.paginationThreshold) {
          console.log(
            `Page ${page} is beyond threshold ${rule.paginationThreshold}, checking user limits`
          );
          await checkUserLimits(ctx, strapi);
        } else {
          console.log(`Page ${page || 'not specified'} does not require authentication`);
        }
      } else if (trackMode === 'path') {
        // Handle path-based tracking (track all matching requests)
        console.log('Path requires authentication regardless of pagination');
        if (ctx.state.user) {
          await checkUserLimits(ctx, strapi);
        } else {
          throw new UnauthorizedError('Authentication required for this resource');
        }
      }
    } else {
      console.log('Request not eligible for tracking');
    }

    // Continue processing the request
    await next();

    // After the response is sent, track request if needed
    if (ctx.status === 200 && shouldTrackRequest(ctx, mergedConfig)) {
      const rule = getTrackingRule(ctx, mergedConfig);
      const trackMode = rule.trackMode || 'pagination';

      if (trackMode === 'pagination') {
        // Only track pagination requests that exceed the threshold
        const { pagination } = ctx.query || {};
        const page = pagination?.page;

        if (page && Number(page) > rule.paginationThreshold && ctx.state.user) {
          console.log('Tracking paginated request after successful response');
          await trackUserRequest(ctx, strapi);
        }
      } else if (trackMode === 'path' && ctx.state.user) {
        // Track all path-based requests regardless of pagination
        console.log('Tracking path-based request after successful response');
        await trackUserRequest(ctx, strapi);
      }
    }
  };
};

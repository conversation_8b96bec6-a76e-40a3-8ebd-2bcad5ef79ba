#!/bin/bash

# Script to move contents from affitor-X directory to affitor-(X+1) directory
# Usage: Run this script from within an affitor-X directory

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show usage
show_usage() {
    print_message $BLUE "Usage: Run this script from within an 'affitor-X' directory"
    print_message $BLUE "The script will create 'affitor-(X+1)' and move all contents there"
}

# Get the current directory name (not full path)
current_dir=$(basename "$(pwd)")
parent_dir=$(dirname "$(pwd)")

print_message $BLUE "Current directory: $current_dir"
print_message $BLUE "Parent directory: $parent_dir"

# Check if current directory follows the affitor-X pattern
if [[ ! $current_dir =~ ^affitor-([0-9]+)$ ]]; then
    print_message $RED "Error: Current directory '$current_dir' does not follow the 'affitor-X' pattern"
    print_message $RED "Expected pattern: affitor-NUMBER (e.g., affitor-10, affitor-5)"
    show_usage
    exit 1
fi

# Extract the number from the directory name
current_number=${BASH_REMATCH[1]}
next_number=$((current_number + 1))
new_dir_name="affitor-$next_number"
new_dir_path="$parent_dir/$new_dir_name"

print_message $YELLOW "Current affitor number: $current_number"
print_message $YELLOW "Next affitor number: $next_number"
print_message $YELLOW "New directory will be: $new_dir_name"

# Check if the new directory already exists
if [ -d "$new_dir_path" ]; then
    print_message $RED "Error: Directory '$new_dir_name' already exists at '$new_dir_path'"
    print_message $RED "Please remove or rename the existing directory first"
    exit 1
fi

# Create the new directory
print_message $BLUE "Creating new directory: $new_dir_path"
mkdir "$new_dir_path"

if [ ! -d "$new_dir_path" ]; then
    print_message $RED "Error: Failed to create directory '$new_dir_path'"
    exit 1
fi

# Move all contents (including hidden files) to the new directory
print_message $BLUE "Moving all contents to $new_dir_name..."

# Enable dotglob to include hidden files in glob expansion
shopt -s dotglob

# Count files/directories to move (excluding . and ..)
file_count=0
for item in *; do
    if [[ "$item" != "." && "$item" != ".." ]]; then
        ((file_count++))
    fi
done

if [ $file_count -eq 0 ]; then
    print_message $YELLOW "No files to move (directory is empty)"
else
    print_message $BLUE "Moving $file_count items..."
    
    # Move all files and directories (including hidden ones)
    for item in *; do
        if [[ "$item" != "." && "$item" != ".." ]]; then
            print_message $BLUE "Moving: $item"
            mv "$item" "$new_dir_path/"
        fi
    done
fi

# Disable dotglob
shopt -u dotglob

# Verify the move was successful
remaining_items=$(find . -maxdepth 1 -not -name "." -not -name ".." | wc -l)
if [ $remaining_items -gt 0 ]; then
    print_message $RED "Warning: Some items may not have been moved successfully"
    print_message $RED "Remaining items in current directory:"
    ls -la
else
    print_message $GREEN "✓ All contents moved successfully!"
fi

# Change to the new directory
print_message $BLUE "Changing to new directory: $new_dir_path"
cd "$new_dir_path"

# Verify we're in the correct directory
if [ "$(basename "$(pwd)")" != "$new_dir_name" ]; then
    print_message $RED "Error: Failed to change to new directory"
    exit 1
fi

print_message $GREEN "✓ Successfully changed to: $(pwd)"

# Open VS Code in the new directory
print_message $BLUE "Opening VS Code in the new directory..."
if command -v code >/dev/null 2>&1; then
    code .
    print_message $GREEN "✓ VS Code opened successfully!"
else
    print_message $YELLOW "Warning: 'code' command not found. Please open VS Code manually."
    print_message $YELLOW "You can install the VS Code command line tools from VS Code:"
    print_message $YELLOW "1. Open VS Code"
    print_message $YELLOW "2. Press Cmd+Shift+P"
    print_message $YELLOW "3. Type 'Shell Command: Install 'code' command in PATH'"
fi

print_message $GREEN "✓ Migration completed successfully!"
print_message $GREEN "You are now in: $(pwd)"
print_message $BLUE "The old directory '$parent_dir/$current_dir' should now be empty and can be removed if desired."